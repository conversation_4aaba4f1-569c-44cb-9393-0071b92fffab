# Flow 创意社交平台数据库设计文档

## 概述

本文档详细描述了Flow创意社交平台的数据库架构设计，该平台是一个治愈系的创意社交平台，支持用户发布多种类型的内容，进行社交互动，参与闪电活动等功能。

## 数据库架构图

```
用户系统 ←→ 内容系统 ←→ 互动系统
    ↓         ↓         ↓
统计系统 ←→ 活动系统 ←→ 管理系统
    ↓         ↓         ↓
监控系统 ←→ 通知系统 ←→ 配置系统
```

## 核心表结构

### 1. 用户系统 (User System)

#### 1.1 users - 用户基础信息表
- **主要功能**: 存储用户的基本信息和账户状态
- **关键字段**:
  - `id`: 用户唯一标识
  - `username`: 用户名（唯一）
  - `email`: 邮箱（唯一）
  - `status`: 用户状态（active/inactive/banned）
  - `last_active_at`: 最后活跃时间
- **业务逻辑**: 支持用户注册、登录、状态管理

#### 1.2 user_relationships - 用户关系表
- **主要功能**: 管理用户之间的关注关系
- **关键字段**:
  - `follower_id`: 关注者ID
  - `following_id`: 被关注者ID
  - `status`: 关系状态（pending/accepted/blocked）
- **业务逻辑**: 实现关注/取消关注功能

#### 1.3 user_sessions - 用户会话表
- **主要功能**: 管理用户登录会话和设备信息
- **关键字段**:
  - `session_token`: 会话令牌
  - `device_type`: 设备类型
  - `expires_at`: 过期时间
- **业务逻辑**: 支持多设备登录，会话管理

#### 1.4 user_stats - 用户统计表
- **主要功能**: 存储用户的统计数据和分数
- **关键字段**:
  - `content_count`: 发布内容数
  - `follower_count`: 粉丝数
  - `resonance_score`: 共振分数
- **业务逻辑**: 用于用户排行榜和推荐算法

### 2. 内容系统 (Content System)

#### 2.1 contents - 内容表
- **主要功能**: 存储用户发布的各类内容
- **关键字段**:
  - `type`: 内容类型（text/image/video/audio）
  - `mood`: 心境标签
  - `status`: 内容状态（draft/pending/approved/rejected）
  - `visibility`: 可见性（public/friends/private）
- **业务逻辑**: 支持内容发布、审核、展示

#### 2.2 media_files - 媒体文件表
- **主要功能**: 管理上传的媒体文件
- **关键字段**:
  - `file_type`: 文件类型
  - `file_size`: 文件大小
  - `status`: 处理状态
- **业务逻辑**: 文件上传、处理、存储管理

#### 2.3 tags - 标签表
- **主要功能**: 管理内容标签系统
- **关键字段**:
  - `name`: 标签名称
  - `category`: 标签分类
  - `usage_count`: 使用次数
- **业务逻辑**: 标签分类、热门标签统计

#### 2.4 content_tags - 内容标签关联表
- **主要功能**: 建立内容与标签的多对多关系
- **业务逻辑**: 支持内容标签化和按标签搜索

### 3. 互动系统 (Interaction System)

#### 3.1 likes - 点赞表
- **主要功能**: 记录用户对内容的点赞行为
- **关键字段**:
  - `type`: 反应类型（like/love/wow/sad/angry）
- **业务逻辑**: 支持多种反应类型，防重复点赞

#### 3.2 comments - 评论表
- **主要功能**: 管理内容评论和回复
- **关键字段**:
  - `parent_id`: 父评论ID（支持回复）
  - `status`: 评论状态
- **业务逻辑**: 支持评论回复、评论管理

#### 3.3 shares - 分享表
- **主要功能**: 记录内容分享行为
- **关键字段**:
  - `platform`: 分享平台
- **业务逻辑**: 跨平台分享统计

#### 3.4 reports - 举报表
- **主要功能**: 处理用户举报
- **关键字段**:
  - `reason`: 举报原因
  - `status`: 处理状态
- **业务逻辑**: 举报处理流程

### 4. 活动系统 (Activity System)

#### 4.1 lightning_activities - 闪电活动表
- **主要功能**: 管理平台举办的各类活动
- **关键字段**:
  - `theme`: 活动主题
  - `start_time/end_time`: 活动时间
  - `max_participants`: 最大参与人数
- **业务逻辑**: 活动创建、管理、状态控制

#### 4.2 activity_participations - 活动参与表
- **主要功能**: 记录用户参与活动的情况
- **关键字段**:
  - `status`: 参与状态
  - `score`: 得分
  - `rank_position`: 排名
- **业务逻辑**: 活动参与、排名、奖励发放

### 5. 管理系统 (Admin System)

#### 5.1 admins - 管理员表
- **主要功能**: 管理后台管理员账户
- **关键字段**:
  - `role`: 管理员角色
  - `permissions`: 权限设置
  - `two_factor_enabled`: 双因素认证
- **业务逻辑**: 管理员权限控制、安全认证

#### 5.2 admin_logs - 操作日志表
- **主要功能**: 记录管理员操作日志
- **关键字段**:
  - `action`: 操作动作
  - `target_type/target_id`: 操作目标
- **业务逻辑**: 操作审计、安全监控

#### 5.3 system_settings - 系统配置表
- **主要功能**: 存储系统配置参数
- **关键字段**:
  - `category`: 配置分类
  - `setting_key/setting_value`: 配置键值
- **业务逻辑**: 系统参数动态配置

### 6. 通知系统 (Notification System)

#### 6.1 notifications - 通知表
- **主要功能**: 管理系统通知消息
- **关键字段**:
  - `type`: 通知类型
  - `related_id/related_type`: 关联对象
  - `is_read`: 是否已读
- **业务逻辑**: 消息推送、已读状态管理

### 7. 监控统计系统 (Monitoring & Statistics)

#### 7.1 system_monitoring - 系统监控表
- **主要功能**: 存储系统性能监控数据
- **关键字段**:
  - `metric_name`: 指标名称
  - `metric_value`: 指标值
  - `status`: 状态（normal/warning/critical）
- **业务逻辑**: 系统性能监控、告警

#### 7.2 daily_stats - 每日统计表
- **主要功能**: 汇总每日运营数据
- **关键字段**:
  - `new_users`: 新增用户数
  - `active_users`: 活跃用户数
  - `retention_rate`: 留存率
- **业务逻辑**: 数据分析、运营报表

## 数据库关系图

```mermaid
erDiagram
    users ||--o{ contents : "发布"
    users ||--o{ likes : "点赞"
    users ||--o{ comments : "评论"
    users ||--o{ shares : "分享"
    users ||--o{ user_relationships : "关注"
    users ||--|| user_stats : "统计"
    
    contents ||--o{ likes : "被点赞"
    contents ||--o{ comments : "被评论"
    contents ||--o{ shares : "被分享"
    contents ||--o{ content_tags : "标签"
    contents ||--o{ reports : "被举报"
    
    tags ||--o{ content_tags : "关联"
    
    lightning_activities ||--o{ activity_participations : "参与"
    users ||--o{ activity_participations : "参与者"
    
    admins ||--o{ admin_logs : "操作"
    users ||--o{ notifications : "接收"
```

## 性能优化策略

### 1. 索引策略
- **主键索引**: 所有表都有自增主键
- **唯一索引**: 用户名、邮箱等唯一字段
- **复合索引**: 常用查询组合字段
- **全文索引**: 内容搜索字段

### 2. 触发器优化
- 自动更新计数字段（点赞数、评论数等）
- 自动维护用户统计数据
- 自动更新标签使用次数

### 3. 存储过程
- 复杂的统计计算逻辑
- 批量数据处理
- 定时任务执行

### 4. 视图优化
- 简化复杂查询
- 提供常用数据组合
- 隐藏敏感字段

## 扩展性设计

### 1. 水平扩展
- 支持分库分表
- 读写分离
- 缓存层设计

### 2. 功能扩展
- JSON字段存储灵活数据
- 预留扩展字段
- 模块化设计

### 3. 数据归档
- 历史数据分区
- 冷热数据分离
- 定期清理策略

## 安全考虑

### 1. 数据安全
- 密码哈希存储
- 敏感信息加密
- 访问权限控制

### 2. 操作安全
- 管理员操作日志
- 双因素认证
- 会话管理

### 3. 数据完整性
- 外键约束
- 数据验证
- 事务处理

## 维护建议

### 1. 定期任务
- 每日统计数据生成
- 过期数据清理
- 用户分数更新
- 系统监控数据归档

### 2. 性能监控
- 慢查询分析
- 索引使用情况
- 存储空间监控
- 并发性能测试

### 3. 备份策略
- 定期全量备份
- 增量备份
- 异地备份
- 恢复测试

这个数据库设计充分考虑了Flow平台的业务需求，具有良好的扩展性和性能，能够支撑平台的长期发展。
