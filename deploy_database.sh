#!/bin/bash

# Flow 创意社交平台数据库部署脚本
# 用于自动化部署数据库架构

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
DB_HOST=${DB_HOST:-"localhost"}
DB_PORT=${DB_PORT:-"3306"}
DB_USER=${DB_USER:-"root"}
DB_PASSWORD=${DB_PASSWORD:-""}
DB_NAME=${DB_NAME:-"flow_platform"}
BACKUP_DIR=${BACKUP_DIR:-"./backups"}
SQL_FILE="database_design.sql"

# 检查必要的工具
check_requirements() {
    log_info "检查系统要求..."
    
    if ! command -v mysql &> /dev/null; then
        log_error "MySQL客户端未安装，请先安装MySQL"
        exit 1
    fi
    
    if [ ! -f "$SQL_FILE" ]; then
        log_error "数据库脚本文件 $SQL_FILE 不存在"
        exit 1
    fi
    
    log_success "系统要求检查通过"
}

# 测试数据库连接
test_connection() {
    log_info "测试数据库连接..."
    
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" &> /dev/null; then
        log_success "数据库连接成功"
    else
        log_error "数据库连接失败，请检查连接参数"
        exit 1
    fi
}

# 备份现有数据库（如果存在）
backup_database() {
    log_info "检查是否需要备份现有数据库..."
    
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" &> /dev/null; then
        log_warning "发现现有数据库 $DB_NAME"
        
        # 创建备份目录
        mkdir -p "$BACKUP_DIR"
        
        # 生成备份文件名
        BACKUP_FILE="$BACKUP_DIR/${DB_NAME}_backup_$(date +%Y%m%d_%H%M%S).sql"
        
        log_info "正在备份数据库到 $BACKUP_FILE..."
        
        if mysqldump -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
            --single-transaction --routines --triggers "$DB_NAME" > "$BACKUP_FILE"; then
            log_success "数据库备份完成: $BACKUP_FILE"
        else
            log_error "数据库备份失败"
            exit 1
        fi
    else
        log_info "未发现现有数据库，跳过备份"
    fi
}

# 部署数据库
deploy_database() {
    log_info "开始部署数据库架构..."
    
    # 执行SQL脚本
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" < "$SQL_FILE"; then
        log_success "数据库架构部署完成"
    else
        log_error "数据库架构部署失败"
        exit 1
    fi
}

# 验证部署结果
verify_deployment() {
    log_info "验证数据库部署结果..."
    
    # 检查数据库是否存在
    if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" &> /dev/null; then
        log_error "数据库 $DB_NAME 不存在"
        exit 1
    fi
    
    # 检查关键表是否存在
    TABLES=("users" "contents" "admins" "system_settings")
    for table in "${TABLES[@]}"; do
        if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME; DESCRIBE $table;" &> /dev/null; then
            log_success "表 $table 创建成功"
        else
            log_error "表 $table 创建失败"
            exit 1
        fi
    done
    
    # 检查触发器是否存在
    TRIGGER_COUNT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
        -e "USE $DB_NAME; SELECT COUNT(*) FROM information_schema.triggers WHERE trigger_schema='$DB_NAME';" \
        -s -N)
    
    if [ "$TRIGGER_COUNT" -gt 0 ]; then
        log_success "触发器创建成功 ($TRIGGER_COUNT 个)"
    else
        log_warning "未发现触发器"
    fi
    
    # 检查存储过程是否存在
    PROCEDURE_COUNT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
        -e "USE $DB_NAME; SELECT COUNT(*) FROM information_schema.routines WHERE routine_schema='$DB_NAME' AND routine_type='PROCEDURE';" \
        -s -N)
    
    if [ "$PROCEDURE_COUNT" -gt 0 ]; then
        log_success "存储过程创建成功 ($PROCEDURE_COUNT 个)"
    else
        log_warning "未发现存储过程"
    fi
    
    # 检查视图是否存在
    VIEW_COUNT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
        -e "USE $DB_NAME; SELECT COUNT(*) FROM information_schema.views WHERE table_schema='$DB_NAME';" \
        -s -N)
    
    if [ "$VIEW_COUNT" -gt 0 ]; then
        log_success "视图创建成功 ($VIEW_COUNT 个)"
    else
        log_warning "未发现视图"
    fi
    
    # 检查初始数据
    ADMIN_COUNT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
        -e "USE $DB_NAME; SELECT COUNT(*) FROM admins;" -s -N)
    
    if [ "$ADMIN_COUNT" -gt 0 ]; then
        log_success "初始管理员数据插入成功 ($ADMIN_COUNT 个)"
    else
        log_warning "未发现管理员数据"
    fi
    
    SETTINGS_COUNT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
        -e "USE $DB_NAME; SELECT COUNT(*) FROM system_settings;" -s -N)
    
    if [ "$SETTINGS_COUNT" -gt 0 ]; then
        log_success "系统配置数据插入成功 ($SETTINGS_COUNT 项)"
    else
        log_warning "未发现系统配置数据"
    fi
}

# 创建定时任务脚本
create_cron_jobs() {
    log_info "创建定时任务脚本..."
    
    cat > "maintenance_tasks.sh" << 'EOF'
#!/bin/bash

# Flow平台数据库维护任务脚本

DB_HOST="localhost"
DB_PORT="3306"
DB_USER="root"
DB_PASSWORD=""
DB_NAME="flow_platform"

# 生成每日统计数据
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
    -e "USE $DB_NAME; CALL GenerateDailyStats(CURDATE());"

# 清理过期会话
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
    -e "USE $DB_NAME; DELETE FROM user_sessions WHERE expires_at < NOW();"

# 清理过期通知
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
    -e "USE $DB_NAME; DELETE FROM notifications WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY) AND is_read = TRUE;"

# 清理系统监控历史数据
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" \
    -e "USE $DB_NAME; DELETE FROM system_monitoring WHERE recorded_at < DATE_SUB(NOW(), INTERVAL 90 DAY);"

echo "维护任务执行完成: $(date)"
EOF
    
    chmod +x "maintenance_tasks.sh"
    log_success "维护任务脚本创建完成: maintenance_tasks.sh"
    
    log_info "建议添加以下crontab任务:"
    echo "# 每日凌晨2点执行维护任务"
    echo "0 2 * * * /path/to/maintenance_tasks.sh >> /var/log/flow_maintenance.log 2>&1"
}

# 显示部署信息
show_deployment_info() {
    log_success "数据库部署完成！"
    echo ""
    echo "部署信息:"
    echo "  数据库主机: $DB_HOST:$DB_PORT"
    echo "  数据库名称: $DB_NAME"
    echo "  数据库用户: $DB_USER"
    echo ""
    echo "默认管理员账户:"
    echo "  用户名: admin"
    echo "  邮箱: <EMAIL>"
    echo "  密码: password (请立即修改)"
    echo ""
    echo "下一步操作:"
    echo "  1. 修改默认管理员密码"
    echo "  2. 配置应用程序数据库连接"
    echo "  3. 设置定时维护任务"
    echo "  4. 配置数据库备份策略"
    echo ""
    echo "维护脚本: maintenance_tasks.sh"
    echo "备份目录: $BACKUP_DIR"
}

# 主函数
main() {
    echo "========================================"
    echo "Flow 创意社交平台数据库部署脚本"
    echo "========================================"
    echo ""
    
    # 显示配置信息
    log_info "部署配置:"
    echo "  数据库主机: $DB_HOST:$DB_PORT"
    echo "  数据库名称: $DB_NAME"
    echo "  数据库用户: $DB_USER"
    echo "  备份目录: $BACKUP_DIR"
    echo ""
    
    # 确认部署
    read -p "确认开始部署? (y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "部署已取消"
        exit 0
    fi
    
    # 执行部署步骤
    check_requirements
    test_connection
    backup_database
    deploy_database
    verify_deployment
    create_cron_jobs
    show_deployment_info
    
    log_success "部署完成！"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
