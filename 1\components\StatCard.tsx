import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "./ui/card"
import { Badge } from "./ui/badge"
import { TrendingUp, TrendingDown } from "lucide-react"

interface StatCardProps {
  title: string
  value: string
  change?: {
    value: string
    trend: 'up' | 'down'
  }
  icon?: React.ReactNode
  gradient?: string
}

export function StatCard({ title, value, change, icon, gradient = "from-blue-500/10 to-purple-600/10" }: StatCardProps) {
  return (
    <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-card to-card/50 backdrop-blur-sm">
      <div className={`absolute inset-0 bg-gradient-to-br ${gradient} opacity-50`} />
      <CardHeader className="relative flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        {icon && (
          <div className="text-muted-foreground">
            {icon}
          </div>
        )}
      </CardHeader>
      <CardContent className="relative">
        <div className="text-2xl font-bold">{value}</div>
        {change && (
          <div className="flex items-center gap-1 mt-1">
            <Badge variant={change.trend === 'up' ? 'default' : 'destructive'} className="text-xs">
              {change.trend === 'up' ? (
                <TrendingUp className="w-3 h-3 mr-1" />
              ) : (
                <TrendingDown className="w-3 h-3 mr-1" />
              )}
              {change.value}
            </Badge>
          </div>
        )}
      </CardContent>
    </Card>
  )
}