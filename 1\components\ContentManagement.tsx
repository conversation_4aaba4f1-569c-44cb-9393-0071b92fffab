import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "./ui/card"
import { <PERSON>ge } from "./ui/badge"
import { But<PERSON> } from "./ui/button"
import { Avatar, AvatarFallback } from "./ui/avatar"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "./ui/table"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "./ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select"
import { AlertTriangle, CheckCircle, XCircle, Eye, Heart, MessageSquare, Share2, TrendingUp } from "lucide-react"

interface Content {
  id: string
  author: string
  type: 'text' | 'image' | 'video' | 'audio'
  title: string
  content: string
  status: 'pending' | 'approved' | 'rejected'
  publishDate: string
  resonanceCount: number
  reportCount: number
  mood: string
}

const mockContents: Content[] = [
  {
    id: '1',
    author: '张小明',
    type: 'text',
    title: '今天的心情',
    content: '阳光很好，心情也很好。感谢生活中的每一个美好瞬间。',
    status: 'pending',
    publishDate: '2024-02-01',
    resonanceCount: 23,
    reportCount: 0,
    mood: '快乐'
  },
  {
    id: '2',
    author: '李小红',
    type: 'image',
    title: '春天的花朵',
    content: '一张美丽的花朵照片',
    status: 'approved',
    publishDate: '2024-02-01',
    resonanceCount: 156,
    reportCount: 0,
    mood: '平静'
  },
  {
    id: '3',
    author: '王小军',
    type: 'video',
    title: '生活日常',
    content: '分享我的日常生活片段',
    status: 'pending',
    publishDate: '2024-02-01',
    resonanceCount: 45,
    reportCount: 2,
    mood: '兴奋'
  },
  {
    id: '4',
    author: '陈小美',
    type: 'text',
    title: '深夜思考',
    content: '夜深人静的时候，总是会想很多事情...',
    status: 'rejected',
    publishDate: '2024-02-01',
    resonanceCount: 12,
    reportCount: 5,
    mood: '忧郁'
  }
]

const moodStats = [
  { mood: '快乐', count: 1245, color: 'bg-yellow-500' },
  { mood: '平静', count: 987, color: 'bg-blue-500' },
  { mood: '兴奋', count: 756, color: 'bg-red-500' },
  { mood: '忧郁', count: 432, color: 'bg-purple-500' },
  { mood: '愤怒', count: 234, color: 'bg-orange-500' },
  { mood: '焦虑', count: 156, color: 'bg-gray-500' },
]

export function ContentManagement() {
  const [statusFilter, setStatusFilter] = useState<string>('all')

  const filteredContents = mockContents.filter(content => 
    statusFilter === 'all' || content.status === statusFilter
  )

  const getStatusBadge = (status: Content['status']) => {
    switch (status) {
      case 'approved':
        return <Badge className="bg-green-100 text-green-700 hover:bg-green-100">已通过</Badge>
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-700 hover:bg-yellow-100">待审核</Badge>
      case 'rejected':
        return <Badge variant="destructive">已拒绝</Badge>
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  const getTypeIcon = (type: Content['type']) => {
    switch (type) {
      case 'text':
        return '📝'
      case 'image':
        return '🖼️'
      case 'video':
        return '🎥'
      case 'audio':
        return '🎵'
      default:
        return '📄'
    }
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          内容管理
        </h1>
        <p className="text-muted-foreground">管理和审核平台内容</p>
      </div>

      <Tabs defaultValue="content" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="content">内容审核</TabsTrigger>
          <TabsTrigger value="analytics">内容分析</TabsTrigger>
          <TabsTrigger value="mood">心境分布</TabsTrigger>
        </TabsList>

        <TabsContent value="content" className="space-y-6">
          {/* 筛选栏 */}
          <Card className="bg-gradient-to-br from-card to-card/50 backdrop-blur-sm border-0">
            <CardContent className="p-4">
              <div className="flex justify-between items-center">
                <div className="flex gap-4 items-center">
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部状态</SelectItem>
                      <SelectItem value="pending">待审核</SelectItem>
                      <SelectItem value="approved">已通过</SelectItem>
                      <SelectItem value="rejected">已拒绝</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex gap-2">
                  <Badge variant="outline" className="gap-1">
                    <AlertTriangle className="w-3 h-3" />
                    待审核: {mockContents.filter(c => c.status === 'pending').length}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 内容列表 */}
          <Card className="bg-gradient-to-br from-card to-card/50 backdrop-blur-sm border-0">
            <CardHeader>
              <CardTitle>内容列表</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>内容</TableHead>
                    <TableHead>作者</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>心境</TableHead>
                    <TableHead>共振数</TableHead>
                    <TableHead>举报数</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredContents.map((content) => (
                    <TableRow key={content.id} className="hover:bg-muted/30">
                      <TableCell>
                        <div>
                          <p className="font-medium">{content.title}</p>
                          <p className="text-sm text-muted-foreground truncate max-w-60">
                            {content.content}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Avatar className="w-8 h-8">
                            <AvatarFallback className="bg-gradient-to-br from-blue-400 to-purple-500 text-white text-xs">
                              {content.author.charAt(0)}
                            </AvatarFallback>
                          </Avatar>
                          {content.author}
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="flex items-center gap-1">
                          {getTypeIcon(content.type)}
                          {content.type}
                        </span>
                      </TableCell>
                      <TableCell>{getStatusBadge(content.status)}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{content.mood}</Badge>
                      </TableCell>
                      <TableCell>
                        <span className="flex items-center gap-1">
                          <Heart className="w-4 h-4 text-pink-500" />
                          {content.resonanceCount}
                        </span>
                      </TableCell>
                      <TableCell>
                        {content.reportCount > 0 ? (
                          <Badge variant="destructive">{content.reportCount}</Badge>
                        ) : (
                          <span className="text-muted-foreground">0</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button size="sm" variant="ghost">
                            <Eye className="w-4 h-4" />
                          </Button>
                          {content.status === 'pending' && (
                            <>
                              <Button size="sm" variant="ghost" className="text-green-600">
                                <CheckCircle className="w-4 h-4" />
                              </Button>
                              <Button size="sm" variant="ghost" className="text-red-600">
                                <XCircle className="w-4 h-4" />
                              </Button>
                            </>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-0">
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <MessageSquare className="w-5 h-5 text-blue-600" />
                  <div>
                    <p className="text-sm text-muted-foreground">今日发布</p>
                    <p className="text-2xl font-bold">142</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="bg-gradient-to-br from-green-50 to-green-100 border-0">
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Heart className="w-5 h-5 text-green-600" />
                  <div>
                    <p className="text-sm text-muted-foreground">今日共振</p>
                    <p className="text-2xl font-bold">1,247</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-0">
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Share2 className="w-5 h-5 text-purple-600" />
                  <div>
                    <p className="text-sm text-muted-foreground">今日分享</p>
                    <p className="text-2xl font-bold">89</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-0">
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5 text-orange-600" />
                  <div>
                    <p className="text-sm text-muted-foreground">热门内容</p>
                    <p className="text-2xl font-bold">23</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="mood" className="space-y-6">
          <Card className="bg-gradient-to-br from-card to-card/50 backdrop-blur-sm border-0">
            <CardHeader>
              <CardTitle>心境分布统计</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {moodStats.map((stat) => (
                  <div key={stat.mood} className="flex items-center gap-3 p-4 rounded-lg bg-muted/30">
                    <div className={`w-4 h-4 rounded-full ${stat.color}`}></div>
                    <div className="flex-1">
                      <p className="font-medium">{stat.mood}</p>
                      <p className="text-sm text-muted-foreground">{stat.count} 条内容</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">
                        {((stat.count / moodStats.reduce((acc, cur) => acc + cur.count, 0)) * 100).toFixed(1)}%
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}