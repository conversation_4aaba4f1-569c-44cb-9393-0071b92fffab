import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "./ui/card"
import { Badge } from "./ui/badge"
import { But<PERSON> } from "./ui/button"
import { Avatar, AvatarFallback } from "./ui/avatar"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "./ui/tabs"
import { Server, Database, Wifi, HardDrive, Cpu, Activity, AlertTriangle, CheckCircle, Clock } from "lucide-react"

interface SystemStatus {
  name: string
  status: 'online' | 'warning' | 'offline'
  uptime: string
  responseTime: number
  icon: React.ReactNode
}

interface LiveActivity {
  id: string
  user: string
  action: string
  timestamp: string
  type: 'login' | 'content' | 'interaction' | 'system'
}

const systemStatuses: SystemStatus[] = [
  {
    name: 'Web 服务器',
    status: 'online',
    uptime: '99.9%',
    responseTime: 45,
    icon: <Server className="w-5 h-5" />
  },
  {
    name: '数据库',
    status: 'online',
    uptime: '99.8%',
    responseTime: 12,
    icon: <Database className="w-5 h-5" />
  },
  {
    name: 'API 网关',
    status: 'warning',
    uptime: '98.5%',
    responseTime: 89,
    icon: <Wifi className="w-5 h-5" />
  },
  {
    name: 'CDN',
    status: 'online',
    uptime: '99.9%',
    responseTime: 23,
    icon: <HardDrive className="w-5 h-5" />
  },
]

const mockActivities: LiveActivity[] = [
  { id: '1', user: '张小明', action: '登录系统', timestamp: '刚刚', type: 'login' },
  { id: '2', user: '李小红', action: '发布了新内容', timestamp: '1分钟前', type: 'content' },
  { id: '3', user: '王小军', action: '点赞了内容', timestamp: '2分钟前', type: 'interaction' },
  { id: '4', user: '系统', action: '自动备份完成', timestamp: '3分钟前', type: 'system' },
  { id: '5', user: '陈小美', action: '更新了个人资料', timestamp: '5分钟前', type: 'interaction' },
  { id: '6', user: '刘小强', action: '分享了内容', timestamp: '7分钟前', type: 'interaction' },
  { id: '7', user: '系统', action: '清理缓存完成', timestamp: '10分钟前', type: 'system' },
  { id: '8', user: '周小丽', action: '注册新账户', timestamp: '12分钟前', type: 'login' },
]

export function Monitoring() {
  const [activities, setActivities] = useState<LiveActivity[]>(mockActivities)
  const [onlineUsers, setOnlineUsers] = useState(234)

  // 模拟实时更新
  useEffect(() => {
    const interval = setInterval(() => {
      // 模拟新活动
      const newActivity: LiveActivity = {
        id: Date.now().toString(),
        user: ['张三', '李四', '王五', '赵六'][Math.floor(Math.random() * 4)],
        action: ['登录系统', '发布内容', '点赞', '评论', '分享'][Math.floor(Math.random() * 5)],
        timestamp: '刚刚',
        type: ['login', 'content', 'interaction'][Math.floor(Math.random() * 3)] as LiveActivity['type']
      }
      
      setActivities(prev => [newActivity, ...prev.slice(0, 19)]) // 保持最新20条
      setOnlineUsers(prev => prev + Math.floor(Math.random() * 3) - 1) // 随机变化在线用户数
    }, 5000)

    return () => clearInterval(interval)
  }, [])

  const getStatusColor = (status: SystemStatus['status']) => {
    switch (status) {
      case 'online':
        return 'text-green-600 bg-green-100'
      case 'warning':
        return 'text-yellow-600 bg-yellow-100'
      case 'offline':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusIcon = (status: SystemStatus['status']) => {
    switch (status) {
      case 'online':
        return <CheckCircle className="w-4 h-4" />
      case 'warning':
        return <AlertTriangle className="w-4 h-4" />
      case 'offline':
        return <AlertTriangle className="w-4 h-4" />
      default:
        return <Activity className="w-4 h-4" />
    }
  }

  const getActivityIcon = (type: LiveActivity['type']) => {
    switch (type) {
      case 'login':
        return '🔐'
      case 'content':
        return '📝'
      case 'interaction':
        return '❤️'
      case 'system':
        return '⚙️'
      default:
        return '📊'
    }
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            实时监控
          </h1>
          <p className="text-muted-foreground">监控系统状态和用户活动</p>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 rounded-full bg-green-500 animate-pulse"></div>
          <span className="text-sm text-muted-foreground">实时更新</span>
        </div>
      </div>

      <Tabs defaultValue="status" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="status">系统状态</TabsTrigger>
          <TabsTrigger value="activity">活动流</TabsTrigger>
          <TabsTrigger value="alerts">告警中心</TabsTrigger>
        </TabsList>

        <TabsContent value="status" className="space-y-6">
          {/* 在线用户统计 */}
          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-0">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">当前在线用户</p>
                  <p className="text-3xl font-bold text-green-700">{onlineUsers}</p>
                </div>
                <div className="text-green-600">
                  <Activity className="w-8 h-8" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 系统状态卡片 */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {systemStatuses.map((system) => (
              <Card key={system.name} className="bg-gradient-to-br from-card to-card/50 backdrop-blur-sm border-0">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="text-muted-foreground">
                      {system.icon}
                    </div>
                    <Badge className={getStatusColor(system.status)}>
                      {getStatusIcon(system.status)}
                      {system.status === 'online' && '正常'}
                      {system.status === 'warning' && '警告'}
                      {system.status === 'offline' && '离线'}
                    </Badge>
                  </div>
                  <div>
                    <p className="font-medium">{system.name}</p>
                    <p className="text-sm text-muted-foreground">运行时间: {system.uptime}</p>
                    <p className="text-sm text-muted-foreground">响应时间: {system.responseTime}ms</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* 性能监控 */}
          <div className="grid gap-6 md:grid-cols-3">
            <Card className="bg-gradient-to-br from-card to-card/50 backdrop-blur-sm border-0">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Cpu className="w-5 h-5 text-blue-500" />
                  CPU 使用率
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>当前</span>
                    <span className="font-medium">45%</span>
                  </div>
                  <div className="w-full h-3 rounded-full bg-gray-200">
                    <div className="h-3 rounded-full bg-blue-500" style={{ width: '45%' }}></div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-card to-card/50 backdrop-blur-sm border-0">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <HardDrive className="w-5 h-5 text-green-500" />
                  内存使用率
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>当前</span>
                    <span className="font-medium">67%</span>
                  </div>
                  <div className="w-full h-3 rounded-full bg-gray-200">
                    <div className="h-3 rounded-full bg-green-500" style={{ width: '67%' }}></div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-card to-card/50 backdrop-blur-sm border-0">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="w-5 h-5 text-purple-500" />
                  磁盘使用率
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>当前</span>
                    <span className="font-medium">34%</span>
                  </div>
                  <div className="w-full h-3 rounded-full bg-gray-200">
                    <div className="h-3 rounded-full bg-purple-500" style={{ width: '34%' }}></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="activity" className="space-y-6">
          <Card className="bg-gradient-to-br from-card to-card/50 backdrop-blur-sm border-0">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="w-5 h-5 text-blue-500" />
                实时活动流
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {activities.map((activity) => (
                  <div key={activity.id} className="flex items-center gap-4 p-3 rounded-lg bg-muted/30 backdrop-blur-sm">
                    <div className="text-lg">{getActivityIcon(activity.type)}</div>
                    <Avatar className="w-8 h-8">
                      <AvatarFallback className="bg-gradient-to-br from-blue-400 to-purple-500 text-white text-xs">
                        {activity.user.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <p className="text-sm">
                        <span className="font-medium">{activity.user}</span> {activity.action}
                      </p>
                    </div>
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Clock className="w-3 h-3" />
                      {activity.timestamp}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-6">
          <Card className="bg-gradient-to-br from-card to-card/50 backdrop-blur-sm border-0">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="w-5 h-5 text-orange-500" />
                系统告警
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-4 p-4 rounded-lg bg-yellow-50 border border-yellow-200">
                  <AlertTriangle className="w-5 h-5 text-yellow-600" />
                  <div className="flex-1">
                    <p className="font-medium text-yellow-800">API 网关响应时间过高</p>
                    <p className="text-sm text-yellow-600">当前响应时间 89ms，超过阈值 80ms</p>
                  </div>
                  <Badge variant="outline" className="text-yellow-600 border-yellow-600">警告</Badge>
                </div>

                <div className="flex items-center gap-4 p-4 rounded-lg bg-green-50 border border-green-200">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <div className="flex-1">
                    <p className="font-medium text-green-800">数据库备份完成</p>
                    <p className="text-sm text-green-600">定时备份任务执行成功</p>
                  </div>
                  <Badge variant="outline" className="text-green-600 border-green-600">正常</Badge>
                </div>

                <div className="flex items-center gap-4 p-4 rounded-lg bg-blue-50 border border-blue-200">
                  <Activity className="w-5 h-5 text-blue-600" />
                  <div className="flex-1">
                    <p className="font-medium text-blue-800">用户活跃度增长</p>
                    <p className="text-sm text-blue-600">本周活跃用户增长 15.3%</p>
                  </div>
                  <Badge variant="outline" className="text-blue-600 border-blue-600">信息</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}