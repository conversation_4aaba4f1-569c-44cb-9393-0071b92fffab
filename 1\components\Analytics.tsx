import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "./ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "./ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select"
import { Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell, AreaChart, Area } from "recharts"
import { TrendingUp, Users, FileText, Heart, Activity } from "lucide-react"

const userAnalyticsData = [
  { date: '01-15', newUsers: 45, activeUsers: 234, retention: 76 },
  { date: '01-16', newUsers: 52, activeUsers: 267, retention: 78 },
  { date: '01-17', newUsers: 48, activeUsers: 289, retention: 74 },
  { date: '01-18', newUsers: 61, activeUsers: 312, retention: 81 },
  { date: '01-19', newUsers: 55, activeUsers: 298, retention: 79 },
  { date: '01-20', newUsers: 67, activeUsers: 345, retention: 83 },
  { date: '01-21', newUsers: 72, activeUsers: 378, retention: 85 },
]

const contentAnalyticsData = [
  { category: '文字', count: 1245, growth: 12.5 },
  { category: '图片', count: 987, growth: 8.3 },
  { category: '视频', count: 456, growth: 15.7 },
  { category: '音频', count: 234, growth: 6.2 },
  { category: '其他', count: 123, growth: -2.1 },
]

const engagementData = [
  { time: '00:00', likes: 12, comments: 5, shares: 2 },
  { time: '04:00', likes: 8, comments: 3, shares: 1 },
  { time: '08:00', likes: 45, comments: 18, shares: 8 },
  { time: '12:00', likes: 78, comments: 32, shares: 15 },
  { time: '16:00', likes: 65, comments: 28, shares: 12 },
  { time: '20:00', likes: 92, comments: 45, shares: 23 },
]

const platformHealthData = [
  { metric: '系统可用性', value: 99.9, status: 'excellent' },
  { metric: '响应时间', value: 87.3, status: 'good' },
  { metric: '错误率', value: 0.1, status: 'excellent' },
  { metric: '用户满意度', value: 92.5, status: 'excellent' },
]

export function Analytics() {
  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            数据统计
          </h1>
          <p className="text-muted-foreground">深入了解平台数据和用户行为</p>
        </div>
        <Select defaultValue="7d">
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7d">最近7天</SelectItem>
            <SelectItem value="30d">最近30天</SelectItem>
            <SelectItem value="90d">最近90天</SelectItem>
            <SelectItem value="1y">最近1年</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Tabs defaultValue="users" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="users">用户分析</TabsTrigger>
          <TabsTrigger value="content">内容分析</TabsTrigger>
          <TabsTrigger value="engagement">互动分析</TabsTrigger>
          <TabsTrigger value="health">平台健康</TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="space-y-6">
          {/* 用户指标卡片 */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-0">
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Users className="w-5 h-5 text-blue-600" />
                  <div>
                    <p className="text-sm text-muted-foreground">总用户数</p>
                    <p className="text-2xl font-bold">5,439</p>
                    <p className="text-xs text-green-600">+12.5% 本周</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="bg-gradient-to-br from-green-50 to-green-100 border-0">
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5 text-green-600" />
                  <div>
                    <p className="text-sm text-muted-foreground">活跃用户</p>
                    <p className="text-2xl font-bold">3,247</p>
                    <p className="text-xs text-green-600">+8.3% 本周</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-0">
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Activity className="w-5 h-5 text-purple-600" />
                  <div>
                    <p className="text-sm text-muted-foreground">新用户</p>
                    <p className="text-2xl font-bold">412</p>
                    <p className="text-xs text-green-600">+15.7% 本周</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-0">
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Heart className="w-5 h-5 text-orange-600" />
                  <div>
                    <p className="text-sm text-muted-foreground">留存率</p>
                    <p className="text-2xl font-bold">82.5%</p>
                    <p className="text-xs text-green-600">+3.2% 本周</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 用户趋势图表 */}
          <Card className="bg-gradient-to-br from-card to-card/50 backdrop-blur-sm border-0">
            <CardHeader>
              <CardTitle>用户增长趋势</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <AreaChart data={userAnalyticsData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                  <XAxis dataKey="date" stroke="#64748b" />
                  <YAxis stroke="#64748b" />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: 'rgba(255, 255, 255, 0.95)',
                      border: 'none',
                      borderRadius: '8px',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                    }}
                  />
                  <Area type="monotone" dataKey="activeUsers" stackId="1" stroke="#3b82f6" fill="#3b82f6" fillOpacity={0.6} />
                  <Area type="monotone" dataKey="newUsers" stackId="1" stroke="#8b5cf6" fill="#8b5cf6" fillOpacity={0.6} />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="content" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* 内容类型分布 */}
            <Card className="bg-gradient-to-br from-card to-card/50 backdrop-blur-sm border-0">
              <CardHeader>
                <CardTitle>内容类型分布</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={contentAnalyticsData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                    <XAxis dataKey="category" stroke="#64748b" />
                    <YAxis stroke="#64748b" />
                    <Tooltip />
                    <Bar dataKey="count" fill="#3b82f6" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* 内容增长率 */}
            <Card className="bg-gradient-to-br from-card to-card/50 backdrop-blur-sm border-0">
              <CardHeader>
                <CardTitle>内容增长统计</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {contentAnalyticsData.map((item) => (
                    <div key={item.category} className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                      <div>
                        <p className="font-medium">{item.category}</p>
                        <p className="text-sm text-muted-foreground">{item.count} 条内容</p>
                      </div>
                      <div className={`text-right ${item.growth > 0 ? 'text-green-600' : 'text-red-600'}`}>
                        <p className="font-medium">{item.growth > 0 ? '+' : ''}{item.growth}%</p>
                        <p className="text-xs text-muted-foreground">本周增长</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="engagement" className="space-y-6">
          <Card className="bg-gradient-to-br from-card to-card/50 backdrop-blur-sm border-0">
            <CardHeader>
              <CardTitle>用户互动趋势</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={engagementData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                  <XAxis dataKey="time" stroke="#64748b" />
                  <YAxis stroke="#64748b" />
                  <Tooltip />
                  <Line type="monotone" dataKey="likes" stroke="#f59e0b" strokeWidth={2} name="点赞" />
                  <Line type="monotone" dataKey="comments" stroke="#3b82f6" strokeWidth={2} name="评论" />
                  <Line type="monotone" dataKey="shares" stroke="#10b981" strokeWidth={2} name="分享" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="health" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {platformHealthData.map((metric) => (
              <Card key={metric.metric} className="bg-gradient-to-br from-card to-card/50 backdrop-blur-sm border-0">
                <CardContent className="p-4">
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">{metric.metric}</p>
                    <p className="text-2xl font-bold">{metric.value}%</p>
                    <div className={`w-full h-2 rounded-full ${
                      metric.status === 'excellent' ? 'bg-green-200' :
                      metric.status === 'good' ? 'bg-yellow-200' : 'bg-red-200'
                    }`}>
                      <div 
                        className={`h-2 rounded-full ${
                          metric.status === 'excellent' ? 'bg-green-500' :
                          metric.status === 'good' ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${metric.value}%` }}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}