-- Flow 创意社交平台数据库设计
-- 基于前端功能需求设计的完整数据库架构

-- 创建数据库
CREATE DATABASE flow_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE flow_platform;

-- 1. 用户表 (users)
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    display_name VARCHAR(100) NOT NULL COMMENT '显示名称',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    bio TEXT COMMENT '个人简介',
    status ENUM('active', 'inactive', 'banned') DEFAULT 'active' COMMENT '用户状态',
    email_verified BOOLEAN DEFAULT FALSE COMMENT '邮箱是否验证',
    phone VARCHAR(20) COMMENT '手机号',
    birth_date DATE COMMENT '生日',
    gender ENUM('male', 'female', 'other', 'prefer_not_to_say') COMMENT '性别',
    location VARCHAR(100) COMMENT '位置',
    website VARCHAR(200) COMMENT '个人网站',
    privacy_settings JSON COMMENT '隐私设置',
    notification_settings JSON COMMENT '通知设置',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    last_active_at TIMESTAMP NULL COMMENT '最后活跃时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_last_active_at (last_active_at)
) COMMENT '用户基础信息表';

-- 2. 内容表 (contents)
CREATE TABLE contents (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '作者用户ID',
    type ENUM('text', 'image', 'video', 'audio', 'mixed') NOT NULL COMMENT '内容类型',
    title VARCHAR(200) COMMENT '标题',
    content TEXT NOT NULL COMMENT '内容文本',
    mood VARCHAR(50) COMMENT '心境标签',
    tags JSON COMMENT '标签数组',
    media_urls JSON COMMENT '媒体文件URL数组',
    thumbnail_url VARCHAR(500) COMMENT '缩略图URL',
    status ENUM('draft', 'pending', 'approved', 'rejected', 'deleted') DEFAULT 'pending' COMMENT '内容状态',
    visibility ENUM('public', 'friends', 'private') DEFAULT 'public' COMMENT '可见性',
    location VARCHAR(100) COMMENT '发布位置',
    weather VARCHAR(50) COMMENT '天气情况',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否精选',
    is_pinned BOOLEAN DEFAULT FALSE COMMENT '是否置顶',
    view_count INT DEFAULT 0 COMMENT '浏览次数',
    like_count INT DEFAULT 0 COMMENT '点赞数',
    comment_count INT DEFAULT 0 COMMENT '评论数',
    share_count INT DEFAULT 0 COMMENT '分享数',
    report_count INT DEFAULT 0 COMMENT '举报数',
    published_at TIMESTAMP NULL COMMENT '发布时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_mood (mood),
    INDEX idx_published_at (published_at),
    INDEX idx_created_at (created_at),
    INDEX idx_like_count (like_count),
    INDEX idx_view_count (view_count),
    FULLTEXT idx_content_search (title, content)
) COMMENT '内容发布表';

-- 3. 用户关系表 (user_relationships)
CREATE TABLE user_relationships (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    follower_id BIGINT NOT NULL COMMENT '关注者ID',
    following_id BIGINT NOT NULL COMMENT '被关注者ID',
    status ENUM('pending', 'accepted', 'blocked') DEFAULT 'accepted' COMMENT '关系状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (follower_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (following_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_relationship (follower_id, following_id),
    INDEX idx_follower_id (follower_id),
    INDEX idx_following_id (following_id),
    INDEX idx_status (status)
) COMMENT '用户关系表';

-- 4. 点赞表 (likes)
CREATE TABLE likes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    content_id BIGINT NOT NULL COMMENT '内容ID',
    type ENUM('like', 'love', 'wow', 'sad', 'angry') DEFAULT 'like' COMMENT '反应类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES contents(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_content (user_id, content_id),
    INDEX idx_content_id (content_id),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
) COMMENT '点赞反应表';

-- 5. 评论表 (comments)
CREATE TABLE comments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '评论用户ID',
    content_id BIGINT NOT NULL COMMENT '内容ID',
    parent_id BIGINT NULL COMMENT '父评论ID',
    comment_text TEXT NOT NULL COMMENT '评论内容',
    status ENUM('active', 'hidden', 'deleted') DEFAULT 'active' COMMENT '评论状态',
    like_count INT DEFAULT 0 COMMENT '点赞数',
    reply_count INT DEFAULT 0 COMMENT '回复数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES contents(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES comments(id) ON DELETE CASCADE,
    INDEX idx_content_id (content_id),
    INDEX idx_user_id (user_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_created_at (created_at)
) COMMENT '评论表';

-- 6. 分享表 (shares)
CREATE TABLE shares (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '分享用户ID',
    content_id BIGINT NOT NULL COMMENT '内容ID',
    platform ENUM('internal', 'wechat', 'weibo', 'qq', 'douyin', 'other') DEFAULT 'internal' COMMENT '分享平台',
    share_text TEXT COMMENT '分享文案',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES contents(id) ON DELETE CASCADE,
    INDEX idx_content_id (content_id),
    INDEX idx_user_id (user_id),
    INDEX idx_platform (platform),
    INDEX idx_created_at (created_at)
) COMMENT '分享记录表';

-- 7. 举报表 (reports)
CREATE TABLE reports (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    reporter_id BIGINT NOT NULL COMMENT '举报人ID',
    reported_user_id BIGINT NULL COMMENT '被举报用户ID',
    content_id BIGINT NULL COMMENT '被举报内容ID',
    comment_id BIGINT NULL COMMENT '被举报评论ID',
    reason ENUM('spam', 'harassment', 'inappropriate', 'copyright', 'fake', 'other') NOT NULL COMMENT '举报原因',
    description TEXT COMMENT '举报描述',
    status ENUM('pending', 'reviewing', 'resolved', 'dismissed') DEFAULT 'pending' COMMENT '处理状态',
    admin_notes TEXT COMMENT '管理员备注',
    resolved_by BIGINT NULL COMMENT '处理管理员ID',
    resolved_at TIMESTAMP NULL COMMENT '处理时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (reporter_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reported_user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (content_id) REFERENCES contents(id) ON DELETE SET NULL,
    FOREIGN KEY (comment_id) REFERENCES comments(id) ON DELETE SET NULL,
    INDEX idx_reporter_id (reporter_id),
    INDEX idx_reported_user_id (reported_user_id),
    INDEX idx_content_id (content_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) COMMENT '举报记录表';

-- 8. 闪电活动表 (lightning_activities)
CREATE TABLE lightning_activities (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL COMMENT '活动标题',
    description TEXT COMMENT '活动描述',
    theme VARCHAR(100) COMMENT '活动主题',
    cover_image VARCHAR(500) COMMENT '封面图片',
    start_time TIMESTAMP NOT NULL COMMENT '开始时间',
    end_time TIMESTAMP NOT NULL COMMENT '结束时间',
    max_participants INT COMMENT '最大参与人数',
    current_participants INT DEFAULT 0 COMMENT '当前参与人数',
    status ENUM('draft', 'active', 'ended', 'cancelled') DEFAULT 'draft' COMMENT '活动状态',
    rules JSON COMMENT '活动规则',
    rewards JSON COMMENT '奖励设置',
    created_by BIGINT NOT NULL COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_status (status),
    INDEX idx_start_time (start_time),
    INDEX idx_end_time (end_time),
    INDEX idx_created_by (created_by)
) COMMENT '闪电活动表';

-- 9. 活动参与表 (activity_participations)
CREATE TABLE activity_participations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    activity_id BIGINT NOT NULL COMMENT '活动ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    content_id BIGINT NULL COMMENT '参与内容ID',
    status ENUM('joined', 'submitted', 'winner', 'disqualified') DEFAULT 'joined' COMMENT '参与状态',
    score INT DEFAULT 0 COMMENT '得分',
    rank_position INT COMMENT '排名',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    submitted_at TIMESTAMP NULL COMMENT '提交时间',
    
    FOREIGN KEY (activity_id) REFERENCES lightning_activities(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES contents(id) ON DELETE SET NULL,
    UNIQUE KEY uk_activity_user (activity_id, user_id),
    INDEX idx_activity_id (activity_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_score (score)
) COMMENT '活动参与记录表';

-- 10. 系统通知表 (notifications)
CREATE TABLE notifications (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '接收用户ID',
    type ENUM('like', 'comment', 'follow', 'mention', 'system', 'activity') NOT NULL COMMENT '通知类型',
    title VARCHAR(200) NOT NULL COMMENT '通知标题',
    content TEXT COMMENT '通知内容',
    related_id BIGINT COMMENT '相关对象ID',
    related_type ENUM('user', 'content', 'comment', 'activity') COMMENT '相关对象类型',
    is_read BOOLEAN DEFAULT FALSE COMMENT '是否已读',
    is_pushed BOOLEAN DEFAULT FALSE COMMENT '是否已推送',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL COMMENT '阅读时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at)
) COMMENT '系统通知表';

-- 11. 管理员表 (admins)
CREATE TABLE admins (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '管理员用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    display_name VARCHAR(100) NOT NULL COMMENT '显示名称',
    role ENUM('super_admin', 'admin', 'moderator', 'editor') DEFAULT 'moderator' COMMENT '角色',
    permissions JSON COMMENT '权限设置',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT '状态',
    two_factor_enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用双因素认证',
    two_factor_secret VARCHAR(100) COMMENT '双因素认证密钥',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status)
) COMMENT '管理员表';

-- 12. 系统配置表 (system_settings)
CREATE TABLE system_settings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    category VARCHAR(50) NOT NULL COMMENT '配置分类',
    setting_key VARCHAR(100) NOT NULL COMMENT '配置键',
    setting_value TEXT COMMENT '配置值',
    data_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '数据类型',
    description TEXT COMMENT '配置描述',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_category_key (category, setting_key),
    INDEX idx_category (category),
    INDEX idx_is_public (is_public)
) COMMENT '系统配置表';

-- 13. 操作日志表 (admin_logs)
CREATE TABLE admin_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    admin_id BIGINT NOT NULL COMMENT '管理员ID',
    action VARCHAR(100) NOT NULL COMMENT '操作动作',
    target_type VARCHAR(50) COMMENT '目标类型',
    target_id BIGINT COMMENT '目标ID',
    details JSON COMMENT '操作详情',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE,
    INDEX idx_admin_id (admin_id),
    INDEX idx_action (action),
    INDEX idx_target_type (target_type),
    INDEX idx_created_at (created_at)
) COMMENT '管理员操作日志表';

-- 14. 用户统计表 (user_stats)
CREATE TABLE user_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    content_count INT DEFAULT 0 COMMENT '发布内容数',
    follower_count INT DEFAULT 0 COMMENT '粉丝数',
    following_count INT DEFAULT 0 COMMENT '关注数',
    like_received_count INT DEFAULT 0 COMMENT '收到点赞数',
    comment_received_count INT DEFAULT 0 COMMENT '收到评论数',
    share_received_count INT DEFAULT 0 COMMENT '收到分享数',
    total_views INT DEFAULT 0 COMMENT '总浏览量',
    resonance_score DECIMAL(10,2) DEFAULT 0 COMMENT '共振分数',
    activity_score DECIMAL(10,2) DEFAULT 0 COMMENT '活跃分数',
    influence_score DECIMAL(10,2) DEFAULT 0 COMMENT '影响力分数',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_id (user_id),
    INDEX idx_resonance_score (resonance_score),
    INDEX idx_activity_score (activity_score),
    INDEX idx_influence_score (influence_score)
) COMMENT '用户统计数据表';

-- 15. 系统监控表 (system_monitoring)
CREATE TABLE system_monitoring (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    metric_name VARCHAR(100) NOT NULL COMMENT '指标名称',
    metric_value DECIMAL(15,4) NOT NULL COMMENT '指标值',
    metric_unit VARCHAR(20) COMMENT '单位',
    server_name VARCHAR(100) COMMENT '服务器名称',
    component VARCHAR(100) COMMENT '组件名称',
    status ENUM('normal', 'warning', 'critical') DEFAULT 'normal' COMMENT '状态',
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_metric_name (metric_name),
    INDEX idx_server_name (server_name),
    INDEX idx_status (status),
    INDEX idx_recorded_at (recorded_at)
) COMMENT '系统监控数据表';

-- 16. 媒体文件表 (media_files)
CREATE TABLE media_files (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '上传用户ID',
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    file_name VARCHAR(255) NOT NULL COMMENT '存储文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_url VARCHAR(500) NOT NULL COMMENT '访问URL',
    file_type ENUM('image', 'video', 'audio', 'document') NOT NULL COMMENT '文件类型',
    mime_type VARCHAR(100) NOT NULL COMMENT 'MIME类型',
    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
    width INT COMMENT '图片/视频宽度',
    height INT COMMENT '图片/视频高度',
    duration INT COMMENT '音视频时长(秒)',
    thumbnail_url VARCHAR(500) COMMENT '缩略图URL',
    status ENUM('uploading', 'processing', 'completed', 'failed', 'deleted') DEFAULT 'uploading' COMMENT '状态',
    is_public BOOLEAN DEFAULT TRUE COMMENT '是否公开',
    download_count INT DEFAULT 0 COMMENT '下载次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_file_type (file_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) COMMENT '媒体文件表';

-- 17. 标签表 (tags)
CREATE TABLE tags (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL UNIQUE COMMENT '标签名称',
    description TEXT COMMENT '标签描述',
    color VARCHAR(7) COMMENT '标签颜色',
    category VARCHAR(50) COMMENT '标签分类',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    is_trending BOOLEAN DEFAULT FALSE COMMENT '是否热门',
    created_by BIGINT COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_name (name),
    INDEX idx_category (category),
    INDEX idx_usage_count (usage_count),
    INDEX idx_is_trending (is_trending)
) COMMENT '标签表';

-- 18. 内容标签关联表 (content_tags)
CREATE TABLE content_tags (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    content_id BIGINT NOT NULL COMMENT '内容ID',
    tag_id BIGINT NOT NULL COMMENT '标签ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (content_id) REFERENCES contents(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE,
    UNIQUE KEY uk_content_tag (content_id, tag_id),
    INDEX idx_content_id (content_id),
    INDEX idx_tag_id (tag_id)
) COMMENT '内容标签关联表';

-- 19. 用户会话表 (user_sessions)
CREATE TABLE user_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    session_token VARCHAR(255) NOT NULL UNIQUE COMMENT '会话令牌',
    device_type ENUM('web', 'ios', 'android', 'other') DEFAULT 'web' COMMENT '设备类型',
    device_info JSON COMMENT '设备信息',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    location VARCHAR(100) COMMENT '地理位置',
    user_agent TEXT COMMENT '用户代理',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否活跃',
    last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后活动时间',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_session_token (session_token),
    INDEX idx_is_active (is_active),
    INDEX idx_expires_at (expires_at),
    INDEX idx_last_activity_at (last_activity_at)
) COMMENT '用户会话表';

-- 20. 数据统计表 (daily_stats)
CREATE TABLE daily_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    stat_date DATE NOT NULL COMMENT '统计日期',
    new_users INT DEFAULT 0 COMMENT '新增用户数',
    active_users INT DEFAULT 0 COMMENT '活跃用户数',
    new_contents INT DEFAULT 0 COMMENT '新增内容数',
    total_likes INT DEFAULT 0 COMMENT '总点赞数',
    total_comments INT DEFAULT 0 COMMENT '总评论数',
    total_shares INT DEFAULT 0 COMMENT '总分享数',
    total_views INT DEFAULT 0 COMMENT '总浏览数',
    retention_rate DECIMAL(5,2) DEFAULT 0 COMMENT '留存率',
    avg_session_duration INT DEFAULT 0 COMMENT '平均会话时长(分钟)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_stat_date (stat_date),
    INDEX idx_stat_date (stat_date)
) COMMENT '每日统计数据表';

-- ================================
-- 触发器定义
-- ================================

-- 触发器：更新内容点赞数
DELIMITER $$
CREATE TRIGGER tr_update_content_like_count
AFTER INSERT ON likes
FOR EACH ROW
BEGIN
    UPDATE contents
    SET like_count = (SELECT COUNT(*) FROM likes WHERE content_id = NEW.content_id)
    WHERE id = NEW.content_id;
END$$

CREATE TRIGGER tr_update_content_like_count_delete
AFTER DELETE ON likes
FOR EACH ROW
BEGIN
    UPDATE contents
    SET like_count = (SELECT COUNT(*) FROM likes WHERE content_id = OLD.content_id)
    WHERE id = OLD.content_id;
END$$

-- 触发器：更新内容评论数
CREATE TRIGGER tr_update_content_comment_count
AFTER INSERT ON comments
FOR EACH ROW
BEGIN
    UPDATE contents
    SET comment_count = (SELECT COUNT(*) FROM comments WHERE content_id = NEW.content_id AND status = 'active')
    WHERE id = NEW.content_id;
END$$

CREATE TRIGGER tr_update_content_comment_count_update
AFTER UPDATE ON comments
FOR EACH ROW
BEGIN
    UPDATE contents
    SET comment_count = (SELECT COUNT(*) FROM comments WHERE content_id = NEW.content_id AND status = 'active')
    WHERE id = NEW.content_id;
END$$

-- 触发器：更新内容分享数
CREATE TRIGGER tr_update_content_share_count
AFTER INSERT ON shares
FOR EACH ROW
BEGIN
    UPDATE contents
    SET share_count = (SELECT COUNT(*) FROM shares WHERE content_id = NEW.content_id)
    WHERE id = NEW.content_id;
END$$

-- 触发器：更新用户统计数据
CREATE TRIGGER tr_update_user_stats_content
AFTER INSERT ON contents
FOR EACH ROW
BEGIN
    INSERT INTO user_stats (user_id, content_count)
    VALUES (NEW.user_id, 1)
    ON DUPLICATE KEY UPDATE
        content_count = content_count + 1,
        updated_at = CURRENT_TIMESTAMP;
END$$

-- 触发器：更新标签使用次数
CREATE TRIGGER tr_update_tag_usage_count
AFTER INSERT ON content_tags
FOR EACH ROW
BEGIN
    UPDATE tags
    SET usage_count = usage_count + 1,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.tag_id;
END$$

CREATE TRIGGER tr_update_tag_usage_count_delete
AFTER DELETE ON content_tags
FOR EACH ROW
BEGIN
    UPDATE tags
    SET usage_count = GREATEST(usage_count - 1, 0),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = OLD.tag_id;
END$$

-- 触发器：更新用户最后活跃时间
CREATE TRIGGER tr_update_user_last_active
AFTER UPDATE ON user_sessions
FOR EACH ROW
BEGIN
    IF NEW.last_activity_at > OLD.last_activity_at THEN
        UPDATE users
        SET last_active_at = NEW.last_activity_at
        WHERE id = NEW.user_id;
    END IF;
END$$

DELIMITER ;

-- ================================
-- 存储过程定义
-- ================================

-- 存储过程：计算用户共振分数
DELIMITER $$
CREATE PROCEDURE CalculateUserResonanceScore(IN user_id BIGINT)
BEGIN
    DECLARE total_likes INT DEFAULT 0;
    DECLARE total_comments INT DEFAULT 0;
    DECLARE total_shares INT DEFAULT 0;
    DECLARE total_views INT DEFAULT 0;
    DECLARE content_count INT DEFAULT 0;
    DECLARE resonance_score DECIMAL(10,2) DEFAULT 0;

    -- 获取用户内容的互动数据
    SELECT
        COALESCE(SUM(like_count), 0),
        COALESCE(SUM(comment_count), 0),
        COALESCE(SUM(share_count), 0),
        COALESCE(SUM(view_count), 0),
        COUNT(*)
    INTO total_likes, total_comments, total_shares, total_views, content_count
    FROM contents
    WHERE user_id = user_id AND status = 'approved';

    -- 计算共振分数 (加权计算)
    SET resonance_score = (total_likes * 1.0 + total_comments * 2.0 + total_shares * 3.0 + total_views * 0.1) / GREATEST(content_count, 1);

    -- 更新用户统计
    INSERT INTO user_stats (user_id, like_received_count, comment_received_count, share_received_count, total_views, resonance_score)
    VALUES (user_id, total_likes, total_comments, total_shares, total_views, resonance_score)
    ON DUPLICATE KEY UPDATE
        like_received_count = total_likes,
        comment_received_count = total_comments,
        share_received_count = total_shares,
        total_views = total_views,
        resonance_score = resonance_score,
        updated_at = CURRENT_TIMESTAMP;
END$$

-- 存储过程：生成每日统计数据
CREATE PROCEDURE GenerateDailyStats(IN target_date DATE)
BEGIN
    DECLARE new_users_count INT DEFAULT 0;
    DECLARE active_users_count INT DEFAULT 0;
    DECLARE new_contents_count INT DEFAULT 0;
    DECLARE total_likes_count INT DEFAULT 0;
    DECLARE total_comments_count INT DEFAULT 0;
    DECLARE total_shares_count INT DEFAULT 0;
    DECLARE total_views_count INT DEFAULT 0;

    -- 计算新增用户数
    SELECT COUNT(*) INTO new_users_count
    FROM users
    WHERE DATE(created_at) = target_date;

    -- 计算活跃用户数
    SELECT COUNT(DISTINCT user_id) INTO active_users_count
    FROM user_sessions
    WHERE DATE(last_activity_at) = target_date AND is_active = TRUE;

    -- 计算新增内容数
    SELECT COUNT(*) INTO new_contents_count
    FROM contents
    WHERE DATE(created_at) = target_date;

    -- 计算当日互动数据
    SELECT
        COALESCE(SUM(like_count), 0),
        COALESCE(SUM(comment_count), 0),
        COALESCE(SUM(share_count), 0),
        COALESCE(SUM(view_count), 0)
    INTO total_likes_count, total_comments_count, total_shares_count, total_views_count
    FROM contents
    WHERE DATE(created_at) = target_date;

    -- 插入或更新统计数据
    INSERT INTO daily_stats (
        stat_date, new_users, active_users, new_contents,
        total_likes, total_comments, total_shares, total_views
    )
    VALUES (
        target_date, new_users_count, active_users_count, new_contents_count,
        total_likes_count, total_comments_count, total_shares_count, total_views_count
    )
    ON DUPLICATE KEY UPDATE
        new_users = new_users_count,
        active_users = active_users_count,
        new_contents = new_contents_count,
        total_likes = total_likes_count,
        total_comments = total_comments_count,
        total_shares = total_shares_count,
        total_views = total_views_count,
        updated_at = CURRENT_TIMESTAMP;
END$$

DELIMITER ;

-- ================================
-- 视图定义
-- ================================

-- 视图：用户详细信息（包含统计数据）
CREATE VIEW v_user_details AS
SELECT
    u.id,
    u.username,
    u.email,
    u.display_name,
    u.avatar_url,
    u.bio,
    u.status,
    u.created_at,
    u.last_active_at,
    COALESCE(us.content_count, 0) as content_count,
    COALESCE(us.follower_count, 0) as follower_count,
    COALESCE(us.following_count, 0) as following_count,
    COALESCE(us.like_received_count, 0) as like_received_count,
    COALESCE(us.resonance_score, 0) as resonance_score
FROM users u
LEFT JOIN user_stats us ON u.id = us.user_id;

-- 视图：内容详细信息（包含作者信息）
CREATE VIEW v_content_details AS
SELECT
    c.id,
    c.user_id,
    u.username as author_username,
    u.display_name as author_name,
    u.avatar_url as author_avatar,
    c.type,
    c.title,
    c.content,
    c.mood,
    c.tags,
    c.status,
    c.visibility,
    c.like_count,
    c.comment_count,
    c.share_count,
    c.view_count,
    c.published_at,
    c.created_at
FROM contents c
JOIN users u ON c.user_id = u.id;

-- 视图：热门内容
CREATE VIEW v_trending_contents AS
SELECT
    c.*,
    u.username as author_username,
    u.display_name as author_name,
    u.avatar_url as author_avatar,
    (c.like_count * 1.0 + c.comment_count * 2.0 + c.share_count * 3.0 + c.view_count * 0.1) as trending_score
FROM contents c
JOIN users u ON c.user_id = u.id
WHERE c.status = 'approved'
    AND c.visibility = 'public'
    AND c.published_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
ORDER BY trending_score DESC;

-- 视图：系统监控概览
CREATE VIEW v_system_overview AS
SELECT
    DATE(recorded_at) as monitor_date,
    AVG(CASE WHEN metric_name = 'cpu_usage' THEN metric_value END) as avg_cpu_usage,
    AVG(CASE WHEN metric_name = 'memory_usage' THEN metric_value END) as avg_memory_usage,
    AVG(CASE WHEN metric_name = 'disk_usage' THEN metric_value END) as avg_disk_usage,
    AVG(CASE WHEN metric_name = 'response_time' THEN metric_value END) as avg_response_time,
    COUNT(CASE WHEN status = 'critical' THEN 1 END) as critical_alerts,
    COUNT(CASE WHEN status = 'warning' THEN 1 END) as warning_alerts
FROM system_monitoring
WHERE recorded_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(recorded_at)
ORDER BY monitor_date DESC;

-- ================================
-- 初始数据插入
-- ================================

-- 插入默认管理员账户
INSERT INTO admins (username, email, password_hash, display_name, role, status) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '超级管理员', 'super_admin', 'active'),
('moderator', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '内容审核员', 'moderator', 'active');

-- 插入系统配置
INSERT INTO system_settings (category, setting_key, setting_value, data_type, description, is_public) VALUES
('platform', 'site_name', 'Flow 创意社交平台', 'string', '平台名称', TRUE),
('platform', 'site_description', '一个治愈系的创意社交平台', 'string', '平台描述', TRUE),
('platform', 'allow_registration', 'true', 'boolean', '是否允许用户注册', FALSE),
('platform', 'require_email_verification', 'true', 'boolean', '是否需要邮箱验证', FALSE),
('platform', 'max_content_length', '1000', 'number', '最大内容长度', FALSE),
('platform', 'max_file_size', '10485760', 'number', '最大文件大小(字节)', FALSE),
('notification', 'email_notifications', 'true', 'boolean', '邮件通知开关', FALSE),
('notification', 'push_notifications', 'true', 'boolean', '推送通知开关', FALSE),
('security', 'session_timeout', '86400', 'number', '会话超时时间(秒)', FALSE),
('security', 'password_min_length', '8', 'number', '密码最小长度', FALSE),
('security', 'api_rate_limit', '1000', 'number', 'API速率限制(次/小时)', FALSE);

-- 插入默认标签
INSERT INTO tags (name, description, color, category, created_by) VALUES
('快乐', '表达快乐心情的内容', '#FFD700', 'mood', NULL),
('平静', '表达平静心情的内容', '#87CEEB', 'mood', NULL),
('兴奋', '表达兴奋心情的内容', '#FF6347', 'mood', NULL),
('忧郁', '表达忧郁心情的内容', '#9370DB', 'mood', NULL),
('愤怒', '表达愤怒心情的内容', '#DC143C', 'mood', NULL),
('焦虑', '表达焦虑心情的内容', '#696969', 'mood', NULL),
('日常', '日常生活分享', '#32CD32', 'category', NULL),
('美食', '美食相关内容', '#FF8C00', 'category', NULL),
('旅行', '旅行相关内容', '#4169E1', 'category', NULL),
('艺术', '艺术创作内容', '#DA70D6', 'category', NULL),
('音乐', '音乐相关内容', '#FF1493', 'category', NULL),
('摄影', '摄影作品分享', '#00CED1', 'category', NULL);

-- 插入示例用户（用于测试）
INSERT INTO users (username, email, password_hash, display_name, status, email_verified) VALUES
('testuser1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '测试用户1', 'active', TRUE),
('testuser2', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '测试用户2', 'active', TRUE),
('testuser3', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '测试用户3', 'active', TRUE);

-- 初始化用户统计数据
INSERT INTO user_stats (user_id)
SELECT id FROM users WHERE id IN (1, 2, 3);

-- ================================
-- 索引优化建议
-- ================================

-- 复合索引优化
CREATE INDEX idx_contents_user_status_published ON contents(user_id, status, published_at);
CREATE INDEX idx_contents_status_visibility_published ON contents(status, visibility, published_at);
CREATE INDEX idx_likes_content_created ON likes(content_id, created_at);
CREATE INDEX idx_comments_content_status_created ON comments(content_id, status, created_at);
CREATE INDEX idx_notifications_user_read_created ON notifications(user_id, is_read, created_at);
CREATE INDEX idx_user_sessions_user_active_expires ON user_sessions(user_id, is_active, expires_at);

-- 分区表建议（对于大数据量场景）
-- ALTER TABLE admin_logs PARTITION BY RANGE (YEAR(created_at)) (
--     PARTITION p2024 VALUES LESS THAN (2025),
--     PARTITION p2025 VALUES LESS THAN (2026),
--     PARTITION p2026 VALUES LESS THAN (2027),
--     PARTITION p_future VALUES LESS THAN MAXVALUE
-- );

-- ================================
-- 定时任务建议
-- ================================

-- 建议创建以下定时任务：
-- 1. 每日生成统计数据：CALL GenerateDailyStats(CURDATE());
-- 2. 清理过期会话：DELETE FROM user_sessions WHERE expires_at < NOW();
-- 3. 清理过期通知：DELETE FROM notifications WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY) AND is_read = TRUE;
-- 4. 更新用户共振分数：对活跃用户调用 CalculateUserResonanceScore
-- 5. 清理系统监控历史数据：DELETE FROM system_monitoring WHERE recorded_at < DATE_SUB(NOW(), INTERVAL 90 DAY);

-- ================================
-- 数据库设计说明
-- ================================

/*
数据库设计特点：

1. 用户系统：
   - 完整的用户信息管理
   - 用户关系（关注/粉丝）
   - 用户统计数据分离存储
   - 会话管理支持多设备登录

2. 内容系统：
   - 支持多种内容类型（文字、图片、视频、音频）
   - 心境标签系统
   - 内容审核流程
   - 媒体文件独立管理

3. 互动系统：
   - 点赞/反应系统
   - 评论系统（支持回复）
   - 分享系统
   - 举报系统

4. 活动系统：
   - 闪电活动管理
   - 活动参与记录
   - 排名和奖励系统

5. 管理系统：
   - 管理员权限管理
   - 系统配置管理
   - 操作日志记录
   - 系统监控数据

6. 统计分析：
   - 用户行为统计
   - 内容数据分析
   - 系统性能监控
   - 每日数据汇总

7. 性能优化：
   - 合理的索引设计
   - 触发器自动更新计数
   - 视图简化查询
   - 存储过程处理复杂逻辑

8. 扩展性：
   - JSON字段存储灵活数据
   - 标签系统支持分类
   - 通知系统支持多种类型
   - 预留扩展字段

使用建议：
- 定期执行存储过程更新统计数据
- 监控数据库性能，适时调整索引
- 定期清理历史数据，保持性能
- 根据业务发展调整分区策略
*/
