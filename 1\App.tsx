import { useState } from "react"
import { Sidebar<PERSON>rovider, SidebarInset } from "./components/ui/sidebar"
import { AppSidebar } from "./components/AppSidebar"
import { Dashboard } from "./components/Dashboard"
import { UserManagement } from "./components/UserManagement"
import { ContentManagement } from "./components/ContentManagement"
import { Analytics } from "./components/Analytics"
import { Monitoring } from "./components/Monitoring"
import { Settings } from "./components/Settings"
import { Login } from "./components/Login"
import { motion, AnimatePresence } from "motion/react"

export default function App() {
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [activeView, setActiveView] = useState('dashboard')

  const handleLogin = () => {
    setIsLoggedIn(true)
  }

  const handleLogout = () => {
    setIsLoggedIn(false)
    setActiveView('dashboard') // 重置为默认视图
  }

  const renderContent = () => {
    switch (activeView) {
      case 'dashboard':
        return <Dashboard />
      case 'users':
        return <UserManagement />
      case 'content':
        return <ContentManagement />
      case 'analytics':
        return <Analytics />
      case 'monitoring':
        return <Monitoring />
      case 'settings':
        return <Settings onLogout={handleLogout} />
      default:
        return <Dashboard />
    }
  }

  return (
    <AnimatePresence mode="wait">
      {!isLoggedIn ? (
        <motion.div
          key="login"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Login onLogin={handleLogin} />
        </motion.div>
      ) : (
        <motion.div
          key="app"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.5 }}
          className="w-full h-full"
        >
          <SidebarProvider>
            <div className="flex w-full min-h-screen bg-gradient-to-br from-blue-50/30 via-purple-50/30 to-pink-50/30">
              <AppSidebar activeView={activeView} onViewChange={setActiveView} />
              <SidebarInset className="flex-1">
                <motion.div
                  className="p-6"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.2 }}
                >
                  <AnimatePresence mode="wait">
                    <motion.div
                      key={activeView}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      transition={{ duration: 0.3 }}
                    >
                      {renderContent()}
                    </motion.div>
                  </AnimatePresence>
                </motion.div>
              </SidebarInset>
            </div>
          </SidebarProvider>
        </motion.div>
      )}
    </AnimatePresence>
  )
}