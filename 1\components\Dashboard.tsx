import { StatCard } from "./StatCard"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card"
import { Badge } from "./ui/badge"
import { But<PERSON> } from "./ui/button"
import { Users, FileText, Zap, Activity, TrendingUp, MessageSquare, Heart, Star } from "lucide-react"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from "recharts"

const userGrowthData = [
  { month: '1月', users: 1200 },
  { month: '2月', users: 1800 },
  { month: '3月', users: 2400 },
  { month: '4月', users: 3200 },
  { month: '5月', users: 4100 },
  { month: '6月', users: 5500 },
]

const contentData = [
  { name: '文字', value: 400, color: '#8b5cf6' },
  { name: '图片', value: 300, color: '#06b6d4' },
  { name: '视频', value: 200, color: '#10b981' },
  { name: '音频', value: 100, color: '#f59e0b' },
]

const recentActivities = [
  { id: 1, user: '小明', action: '发布了新内容', time: '2分钟前', type: 'content' },
  { id: 2, user: '小红', action: '加入了平台', time: '5分钟前', type: 'user' },
  { id: 3, user: '小李', action: '产生了共振', time: '8分钟前', type: 'interaction' },
  { id: 4, user: '小王', action: '参与了闪电活动', time: '12分钟前', type: 'activity' },
  { id: 5, user: '小张', action: '更新了个人资料', time: '15分钟前', type: 'profile' },
]

export function Dashboard() {
  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            数据概览
          </h1>
          <p className="text-muted-foreground">Flow 创意社交平台实时统计</p>
        </div>
        <Button className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700">
          导出报告
        </Button>
      </div>

      {/* 核心指标卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="总用户数"
          value="5,439"
          change={{ value: "+12.5%", trend: "up" }}
          icon={<Users className="w-4 h-4" />}
          gradient="from-blue-500/20 to-blue-600/20"
        />
        <StatCard
          title="内容总数"
          value="12,847"
          change={{ value: "+8.2%", trend: "up" }}
          icon={<FileText className="w-4 h-4" />}
          gradient="from-green-500/20 to-green-600/20"
        />
        <StatCard
          title="共振数"
          value="45,612"
          change={{ value: "+15.3%", trend: "up" }}
          icon={<Heart className="w-4 h-4" />}
          gradient="from-pink-500/20 to-pink-600/20"
        />
        <StatCard
          title="闪电活动"
          value="89"
          change={{ value: "+23.1%", trend: "up" }}
          icon={<Zap className="w-4 h-4" />}
          gradient="from-yellow-500/20 to-yellow-600/20"
        />
      </div>

      {/* 图表区域 */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* 用户增长趋势 */}
        <Card className="bg-gradient-to-br from-card to-card/50 backdrop-blur-sm border-0">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-blue-500" />
              用户增长趋势
            </CardTitle>
            <CardDescription>最近6个月的用户增长情况</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={userGrowthData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                <XAxis dataKey="month" stroke="#64748b" />
                <YAxis stroke="#64748b" />
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    border: 'none',
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                  }}
                />
                <Line 
                  type="monotone" 
                  dataKey="users" 
                  stroke="url(#gradient)" 
                  strokeWidth={3}
                  dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                />
                <defs>
                  <linearGradient id="gradient" x1="0" y1="0" x2="1" y2="0">
                    <stop offset="0%" stopColor="#3b82f6" />
                    <stop offset="100%" stopColor="#8b5cf6" />
                  </linearGradient>
                </defs>
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* 内容分布 */}
        <Card className="bg-gradient-to-br from-card to-card/50 backdrop-blur-sm border-0">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5 text-purple-500" />
              内容类型分布
            </CardTitle>
            <CardDescription>不同类型内容的分布情况</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={contentData}
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {contentData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* 最近活动 */}
      <Card className="bg-gradient-to-br from-card to-card/50 backdrop-blur-sm border-0">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="w-5 h-5 text-green-500" />
            平台实时活动
          </CardTitle>
          <CardDescription>用户最近的活动动态</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-center gap-4 p-3 rounded-lg bg-muted/30 backdrop-blur-sm">
                <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-500 to-purple-600"></div>
                <div className="flex-1">
                  <p className="text-sm">
                    <span className="font-medium">{activity.user}</span> {activity.action}
                  </p>
                  <p className="text-xs text-muted-foreground">{activity.time}</p>
                </div>
                <Badge variant="outline" className="text-xs">
                  {activity.type === 'content' && '内容'}
                  {activity.type === 'user' && '用户'}
                  {activity.type === 'interaction' && '互动'}
                  {activity.type === 'activity' && '活动'}
                  {activity.type === 'profile' && '资料'}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}