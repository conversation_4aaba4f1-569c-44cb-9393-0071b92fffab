import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card"
import { Button } from "./ui/button"
import { Input } from "./ui/input"
import { Label } from "./ui/label"
import { Switch } from "./ui/switch"
import { Tabs, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "./ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select"
import { Textarea } from "./ui/textarea"
import { Badge } from "./ui/badge"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "./ui/alert-dialog"
import { Settings as SettingsIcon, Shield, Bell, Palette, Database, Mail, Globe, Save, LogOut } from "lucide-react"

interface SettingsProps {
  onLogout?: () => void
}

export function Settings({ onLogout }: SettingsProps) {
  const [platformSettings, setPlatformSettings] = useState({
    siteName: 'Flow 创意社交平台',
    siteDescription: '一个治愈系的创意社交平台',
    allowRegistration: true,
    requireEmailVerification: true,
    enableComments: true,
    enableSharing: true,
    maxContentLength: 1000,
    maxFileSize: 10,
  })

  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    pushNotifications: true,
    smsNotifications: false,
    weeklyReports: true,
    systemAlerts: true,
  })

  const [securitySettings, setSecuritySettings] = useState({
    twoFactorAuth: false,
    sessionTimeout: 24,
    passwordPolicy: 'medium',
    ipWhitelist: '',
    apiRateLimit: 1000,
  })

  const handleSave = (category: string) => {
    // 这里处理保存逻辑
    console.log(`保存 ${category} 设置`)
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          系统设置
        </h1>
        <p className="text-muted-foreground">配置和管理平台各项功能</p>
      </div>

      <Tabs defaultValue="platform" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="platform">平台配置</TabsTrigger>
          <TabsTrigger value="notifications">通知管理</TabsTrigger>
          <TabsTrigger value="security">安全设置</TabsTrigger>
          <TabsTrigger value="appearance">外观设置</TabsTrigger>
          <TabsTrigger value="data">数据管理</TabsTrigger>
          <TabsTrigger value="account">账户管理</TabsTrigger>
        </TabsList>

        <TabsContent value="platform" className="space-y-6">
          <Card className="bg-gradient-to-br from-card to-card/50 backdrop-blur-sm border-0">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <SettingsIcon className="w-5 h-5 text-blue-500" />
                基础设置
              </CardTitle>
              <CardDescription>配置平台的基本信息和功能</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="siteName">平台名称</Label>
                  <Input
                    id="siteName"
                    value={platformSettings.siteName}
                    onChange={(e) => setPlatformSettings(prev => ({ ...prev, siteName: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="maxContentLength">最大内容长度</Label>
                  <Input
                    id="maxContentLength"
                    type="number"
                    value={platformSettings.maxContentLength}
                    onChange={(e) => setPlatformSettings(prev => ({ ...prev, maxContentLength: parseInt(e.target.value) }))}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="siteDescription">平台描述</Label>
                <Textarea
                  id="siteDescription"
                  value={platformSettings.siteDescription}
                  onChange={(e) => setPlatformSettings(prev => ({ ...prev, siteDescription: e.target.value }))}
                  rows={3}
                />
              </div>

              <div className="space-y-4">
                <h4 className="font-medium">功能开关</h4>
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                    <div>
                      <Label htmlFor="allowRegistration">允许用户注册</Label>
                      <p className="text-sm text-muted-foreground">是否允许新用户注册账户</p>
                    </div>
                    <Switch
                      id="allowRegistration"
                      checked={platformSettings.allowRegistration}
                      onCheckedChange={(checked) => setPlatformSettings(prev => ({ ...prev, allowRegistration: checked }))}
                    />
                  </div>

                  <div className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                    <div>
                      <Label htmlFor="requireEmailVerification">邮箱验证</Label>
                      <p className="text-sm text-muted-foreground">注册时需要验证邮箱</p>
                    </div>
                    <Switch
                      id="requireEmailVerification"
                      checked={platformSettings.requireEmailVerification}
                      onCheckedChange={(checked) => setPlatformSettings(prev => ({ ...prev, requireEmailVerification: checked }))}
                    />
                  </div>

                  <div className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                    <div>
                      <Label htmlFor="enableComments">启用评论</Label>
                      <p className="text-sm text-muted-foreground">允许用户对内容进行评论</p>
                    </div>
                    <Switch
                      id="enableComments"
                      checked={platformSettings.enableComments}
                      onCheckedChange={(checked) => setPlatformSettings(prev => ({ ...prev, enableComments: checked }))}
                    />
                  </div>

                  <div className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                    <div>
                      <Label htmlFor="enableSharing">启用分享</Label>
                      <p className="text-sm text-muted-foreground">允许用户分享内容</p>
                    </div>
                    <Switch
                      id="enableSharing"
                      checked={platformSettings.enableSharing}
                      onCheckedChange={(checked) => setPlatformSettings(prev => ({ ...prev, enableSharing: checked }))}
                    />
                  </div>
                </div>
              </div>

              <Button onClick={() => handleSave('platform')} className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700">
                <Save className="w-4 h-4 mr-2" />
                保存平台设置
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-6">
          <Card className="bg-gradient-to-br from-card to-card/50 backdrop-blur-sm border-0">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="w-5 h-5 text-green-500" />
                通知配置
              </CardTitle>
              <CardDescription>配置系统通知和消息推送</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                  <div>
                    <Label htmlFor="emailNotifications">邮件通知</Label>
                    <p className="text-sm text-muted-foreground">发送重要事件的邮件通知</p>
                  </div>
                  <Switch
                    id="emailNotifications"
                    checked={notificationSettings.emailNotifications}
                    onCheckedChange={(checked) => setNotificationSettings(prev => ({ ...prev, emailNotifications: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                  <div>
                    <Label htmlFor="pushNotifications">推送通知</Label>
                    <p className="text-sm text-muted-foreground">向用户设备推送通知</p>
                  </div>
                  <Switch
                    id="pushNotifications"
                    checked={notificationSettings.pushNotifications}
                    onCheckedChange={(checked) => setNotificationSettings(prev => ({ ...prev, pushNotifications: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                  <div>
                    <Label htmlFor="weeklyReports">周报</Label>
                    <p className="text-sm text-muted-foreground">每周发送平台数据报告</p>
                  </div>
                  <Switch
                    id="weeklyReports"
                    checked={notificationSettings.weeklyReports}
                    onCheckedChange={(checked) => setNotificationSettings(prev => ({ ...prev, weeklyReports: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                  <div>
                    <Label htmlFor="systemAlerts">系统告警</Label>
                    <p className="text-sm text-muted-foreground">系统异常时发送告警通知</p>
                  </div>
                  <Switch
                    id="systemAlerts"
                    checked={notificationSettings.systemAlerts}
                    onCheckedChange={(checked) => setNotificationSettings(prev => ({ ...prev, systemAlerts: checked }))}
                  />
                </div>
              </div>

              <Button onClick={() => handleSave('notifications')} className="w-full bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700">
                <Save className="w-4 h-4 mr-2" />
                保存通知设置
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <Card className="bg-gradient-to-br from-card to-card/50 backdrop-blur-sm border-0">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="w-5 h-5 text-red-500" />
                安全配置
              </CardTitle>
              <CardDescription>配置系统安全和访问控制</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="sessionTimeout">会话超时时间 (小时)</Label>
                  <Input
                    id="sessionTimeout"
                    type="number"
                    value={securitySettings.sessionTimeout}
                    onChange={(e) => setSecuritySettings(prev => ({ ...prev, sessionTimeout: parseInt(e.target.value) }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="passwordPolicy">密码策略</Label>
                  <Select
                    value={securitySettings.passwordPolicy}
                    onValueChange={(value) => setSecuritySettings(prev => ({ ...prev, passwordPolicy: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">低强度</SelectItem>
                      <SelectItem value="medium">中强度</SelectItem>
                      <SelectItem value="high">高强度</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="apiRateLimit">API 速率限制 (次/小时)</Label>
                <Input
                  id="apiRateLimit"
                  type="number"
                  value={securitySettings.apiRateLimit}
                  onChange={(e) => setSecuritySettings(prev => ({ ...prev, apiRateLimit: parseInt(e.target.value) }))}
                />
              </div>

              <div className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                <div>
                  <Label htmlFor="twoFactorAuth">双因素认证</Label>
                  <p className="text-sm text-muted-foreground">为管理员账户启用双因素认证</p>
                </div>
                <Switch
                  id="twoFactorAuth"
                  checked={securitySettings.twoFactorAuth}
                  onCheckedChange={(checked) => setSecuritySettings(prev => ({ ...prev, twoFactorAuth: checked }))}
                />
              </div>

              <Button onClick={() => handleSave('security')} className="w-full bg-gradient-to-r from-red-500 to-orange-600 hover:from-red-600 hover:to-orange-700">
                <Save className="w-4 h-4 mr-2" />
                保存安全设置
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="appearance" className="space-y-6">
          <Card className="bg-gradient-to-br from-card to-card/50 backdrop-blur-sm border-0">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="w-5 h-5 text-purple-500" />
                外观配置
              </CardTitle>
              <CardDescription>自定义平台的视觉外观</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label>主题色彩</Label>
                  <div className="grid grid-cols-6 gap-3 mt-2">
                    {['#3b82f6', '#8b5cf6', '#10b981', '#f59e0b', '#ef4444', '#6b7280'].map((color) => (
                      <button
                        key={color}
                        className="w-12 h-12 rounded-lg border-2 border-gray-200 hover:border-gray-400 transition-colors"
                        style={{ backgroundColor: color }}
                      />
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Logo 设置</Label>
                  <div className="flex items-center gap-4 p-4 rounded-lg bg-muted/30">
                    <div className="w-16 h-16 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                      <span className="text-white font-bold">Flow</span>
                    </div>
                    <Button variant="outline">更换 Logo</Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>深色模式</Label>
                  <div className="flex items-center gap-4">
                    <Badge variant="outline">自动</Badge>
                    <Badge variant="outline">浅色</Badge>
                    <Badge variant="outline">深色</Badge>
                  </div>
                </div>
              </div>

              <Button onClick={() => handleSave('appearance')} className="w-full bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700">
                <Save className="w-4 h-4 mr-2" />
                保存外观设置
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="data" className="space-y-6">
          <Card className="bg-gradient-to-br from-card to-card/50 backdrop-blur-sm border-0">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="w-5 h-5 text-orange-500" />
                数据管理
              </CardTitle>
              <CardDescription>备份、恢复和数据保留策略</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="p-4 rounded-lg bg-blue-50 border border-blue-200">
                  <h4 className="font-medium text-blue-800 mb-2">数据备份</h4>
                  <p className="text-sm text-blue-600 mb-3">最近备份: 2024-02-01 03:00</p>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline">立即备份</Button>
                    <Button size="sm" variant="outline">下载备份</Button>
                  </div>
                </div>

                <div className="p-4 rounded-lg bg-green-50 border border-green-200">
                  <h4 className="font-medium text-green-800 mb-2">数据统计</h4>
                  <div className="grid gap-2 text-sm text-green-600">
                    <p>用户数据: 5,439 条记录</p>
                    <p>内容数据: 12,847 条记录</p>
                    <p>互动数据: 45,612 条记录</p>
                    <p>总存储空间: 2.3 GB</p>
                  </div>
                </div>

                <div className="p-4 rounded-lg bg-orange-50 border border-orange-200">
                  <h4 className="font-medium text-orange-800 mb-2">数据清理</h4>
                  <p className="text-sm text-orange-600 mb-3">定期清理过期的临时数据和日志</p>
                  <Button size="sm" variant="outline">开始清理</Button>
                </div>
              </div>

              <Button onClick={() => handleSave('data')} className="w-full bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700">
                <Save className="w-4 h-4 mr-2" />
                保存数据设置
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="account" className="space-y-6">
          <Card className="bg-gradient-to-br from-card to-card/50 backdrop-blur-sm border-0">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <SettingsIcon className="w-5 h-5 text-blue-500" />
                账户管理
              </CardTitle>
              <CardDescription>管理管理员账户和登录设置</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="p-4 rounded-lg bg-blue-50 border border-blue-200">
                  <h4 className="font-medium text-blue-800 mb-2">当前账户信息</h4>
                  <div className="grid gap-2 text-sm text-blue-600">
                    <p>管理员账户: <EMAIL></p>
                    <p>登录时间: 2025-01-15 14:30</p>
                    <p>权限级别: 超级管理员</p>
                    <p>最后活动: 刚刚</p>
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="currentPassword">当前密码</Label>
                    <Input
                      id="currentPassword"
                      type="password"
                      placeholder="请输入当前密码"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="newPassword">新密码</Label>
                    <Input
                      id="newPassword"
                      type="password"
                      placeholder="请输入新密码"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">确认新密码</Label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    placeholder="请再次输入新密码"
                  />
                </div>

                <div className="flex gap-4">
                  <Button className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700">
                    <Save className="w-4 h-4 mr-2" />
                    更新密码
                  </Button>
                  
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="destructive" className="bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700">
                        <LogOut className="w-4 h-4 mr-2" />
                        退出登录
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>确认退出登录</AlertDialogTitle>
                        <AlertDialogDescription>
                          您确定要退出登录吗？退出后需要重新输入凭据才能访问管理系统。
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>取消</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={onLogout}
                          className="bg-red-500 hover:bg-red-600"
                        >
                          确认退出
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>

                <div className="p-4 rounded-lg bg-yellow-50 border border-yellow-200">
                  <h4 className="font-medium text-yellow-800 mb-2">安全提醒</h4>
                  <p className="text-sm text-yellow-600">
                    建议定期更换密码，并启用双因素认证以提高账户安全性。
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}