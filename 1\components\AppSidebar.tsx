import { Home, Users, FileText, BarChart3, Activity, Settings, Zap, TrendingUp, Shield, Bell } from "lucide-react"
import { Sidebar, SidebarContent, SidebarFooter, SidebarGroup, SidebarGroupContent, SidebarGroupLabel, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from "./ui/sidebar"
import { Avatar, AvatarFallback } from "./ui/avatar"

interface AppSidebarProps {
  activeView: string
  onViewChange: (view: string) => void
}

const menuItems = [
  {
    title: "数据概览",
    url: "dashboard",
    icon: Home,
  },
  {
    title: "用户管理",
    url: "users",
    icon: Users,
  },
  {
    title: "内容管理",
    url: "content",
    icon: FileText,
  },
  {
    title: "数据统计",
    url: "analytics",
    icon: BarChart3,
  },
  {
    title: "实时监控",
    url: "monitoring",
    icon: Activity,
  },
  {
    title: "系统设置",
    url: "settings",
    icon: Settings,
  },
]

export function AppSidebar({ activeView, onViewChange }: AppSidebarProps) {
  return (
    <Sidebar>
      <SidebarHeader className="border-b border-sidebar-border">
        <div className="flex items-center gap-2 px-4 py-3">
          <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
            <Zap className="w-4 h-4 text-white" />
          </div>
          <div>
            <h2 className="font-medium">Flow 管理平台</h2>
            <p className="text-xs text-sidebar-foreground/60">创意社交管理后台</p>
          </div>
        </div>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {menuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton 
                    onClick={() => onViewChange(item.url)}
                    isActive={activeView === item.url}
                    className="w-full"
                  >
                    <item.icon className="w-4 h-4" />
                    <span>{item.title}</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter className="border-t border-sidebar-border">
        <div className="flex items-center gap-3 px-4 py-3">
          <Avatar className="w-8 h-8">
            <AvatarFallback className="bg-gradient-to-br from-green-400 to-blue-500 text-white">
              管
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium">管理员</p>
            <p className="text-xs text-sidebar-foreground/60 truncate"><EMAIL></p>
          </div>
        </div>
      </SidebarFooter>
    </Sidebar>
  )
}