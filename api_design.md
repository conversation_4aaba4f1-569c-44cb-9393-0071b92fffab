# Flow 创意社交平台 API 设计文档

## 概述

本文档描述了Flow创意社交平台的RESTful API设计，基于前面设计的数据库架构，为前端应用提供完整的后端服务接口。

## API 基础信息

- **Base URL**: `https://api.flow.com/v1`
- **认证方式**: JWT Token
- **数据格式**: JSON
- **字符编码**: UTF-8

## 认证系统

### 用户认证

#### POST /auth/login
用户登录

**请求体**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "username": "testuser",
      "display_name": "测试用户",
      "avatar_url": "https://cdn.flow.com/avatars/1.jpg"
    }
  }
}
```

#### POST /auth/register
用户注册

**请求体**:
```json
{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "password123",
  "display_name": "新用户"
}
```

#### POST /auth/logout
用户登出

#### POST /auth/refresh
刷新Token

### 管理员认证

#### POST /admin/auth/login
管理员登录

**请求体**:
```json
{
  "username": "admin",
  "password": "admin123",
  "two_factor_code": "123456"
}
```

## 用户管理

### 用户信息

#### GET /users/profile
获取当前用户信息

**响应**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "display_name": "测试用户",
    "avatar_url": "https://cdn.flow.com/avatars/1.jpg",
    "bio": "这是我的个人简介",
    "stats": {
      "content_count": 42,
      "follower_count": 156,
      "following_count": 89,
      "resonance_score": 1234.56
    }
  }
}
```

#### PUT /users/profile
更新用户信息

#### GET /users/{id}
获取指定用户信息

#### GET /users/{id}/contents
获取用户发布的内容

**查询参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20)
- `type`: 内容类型 (text/image/video/audio)

### 用户关系

#### POST /users/{id}/follow
关注用户

#### DELETE /users/{id}/follow
取消关注

#### GET /users/{id}/followers
获取粉丝列表

#### GET /users/{id}/following
获取关注列表

## 内容管理

### 内容发布

#### POST /contents
发布内容

**请求体**:
```json
{
  "type": "text",
  "title": "今天的心情",
  "content": "阳光很好，心情也很好",
  "mood": "快乐",
  "tags": ["日常", "心情"],
  "visibility": "public",
  "media_urls": []
}
```

#### GET /contents
获取内容列表

**查询参数**:
- `page`: 页码
- `limit`: 每页数量
- `type`: 内容类型
- `mood`: 心境标签
- `tag`: 标签筛选
- `sort`: 排序方式 (latest/popular/trending)

#### GET /contents/{id}
获取内容详情

#### PUT /contents/{id}
更新内容

#### DELETE /contents/{id}
删除内容

### 内容互动

#### POST /contents/{id}/like
点赞内容

**请求体**:
```json
{
  "type": "like"
}
```

#### DELETE /contents/{id}/like
取消点赞

#### POST /contents/{id}/comments
发表评论

**请求体**:
```json
{
  "comment_text": "很棒的分享！",
  "parent_id": null
}
```

#### GET /contents/{id}/comments
获取评论列表

#### POST /contents/{id}/share
分享内容

#### POST /contents/{id}/report
举报内容

## 媒体文件

#### POST /media/upload
上传媒体文件

**请求**: multipart/form-data
- `file`: 文件
- `type`: 文件类型

**响应**:
```json
{
  "success": true,
  "data": {
    "id": 123,
    "file_url": "https://cdn.flow.com/media/123.jpg",
    "thumbnail_url": "https://cdn.flow.com/thumbnails/123.jpg"
  }
}
```

## 活动系统

### 闪电活动

#### GET /activities
获取活动列表

#### GET /activities/{id}
获取活动详情

#### POST /activities/{id}/join
参加活动

#### POST /activities/{id}/submit
提交参赛作品

#### GET /activities/{id}/leaderboard
获取活动排行榜

## 通知系统

#### GET /notifications
获取通知列表

#### PUT /notifications/{id}/read
标记通知为已读

#### PUT /notifications/read-all
标记所有通知为已读

## 搜索功能

#### GET /search/contents
搜索内容

**查询参数**:
- `q`: 搜索关键词
- `type`: 内容类型
- `mood`: 心境标签

#### GET /search/users
搜索用户

#### GET /search/tags
搜索标签

## 管理后台 API

### 用户管理

#### GET /admin/users
获取用户列表

**查询参数**:
- `page`: 页码
- `limit`: 每页数量
- `status`: 用户状态
- `search`: 搜索关键词

#### PUT /admin/users/{id}/status
更新用户状态

**请求体**:
```json
{
  "status": "banned",
  "reason": "违规行为"
}
```

### 内容审核

#### GET /admin/contents
获取待审核内容列表

#### PUT /admin/contents/{id}/approve
审核通过

#### PUT /admin/contents/{id}/reject
审核拒绝

### 举报处理

#### GET /admin/reports
获取举报列表

#### PUT /admin/reports/{id}/resolve
处理举报

### 系统监控

#### GET /admin/stats/overview
获取系统概览数据

**响应**:
```json
{
  "success": true,
  "data": {
    "total_users": 5439,
    "active_users": 3247,
    "total_contents": 12847,
    "total_likes": 45612,
    "system_health": {
      "cpu_usage": 45.2,
      "memory_usage": 67.8,
      "disk_usage": 34.1
    }
  }
}
```

#### GET /admin/stats/daily
获取每日统计数据

#### GET /admin/monitoring/alerts
获取系统告警

### 系统配置

#### GET /admin/settings
获取系统配置

#### PUT /admin/settings
更新系统配置

## 错误处理

### 错误响应格式

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求参数验证失败",
    "details": {
      "email": ["邮箱格式不正确"]
    }
  }
}
```

### 常见错误码

- `400` - 请求参数错误
- `401` - 未认证
- `403` - 权限不足
- `404` - 资源不存在
- `409` - 资源冲突
- `422` - 数据验证失败
- `429` - 请求频率限制
- `500` - 服务器内部错误

## 数据库查询示例

### 获取热门内容
```sql
SELECT c.*, u.username, u.display_name, u.avatar_url,
       (c.like_count * 1.0 + c.comment_count * 2.0 + c.share_count * 3.0) as popularity_score
FROM contents c
JOIN users u ON c.user_id = u.id
WHERE c.status = 'approved' 
  AND c.visibility = 'public'
  AND c.published_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
ORDER BY popularity_score DESC
LIMIT 20;
```

### 获取用户动态
```sql
SELECT c.*, u.username, u.display_name, u.avatar_url
FROM contents c
JOIN users u ON c.user_id = u.id
JOIN user_relationships ur ON ur.following_id = c.user_id
WHERE ur.follower_id = ? 
  AND ur.status = 'accepted'
  AND c.status = 'approved'
  AND c.visibility IN ('public', 'friends')
ORDER BY c.published_at DESC
LIMIT 20 OFFSET ?;
```

### 计算用户共振分数
```sql
SELECT user_id,
       SUM(like_count) as total_likes,
       SUM(comment_count) as total_comments,
       SUM(share_count) as total_shares,
       SUM(view_count) as total_views,
       (SUM(like_count) * 1.0 + SUM(comment_count) * 2.0 + SUM(share_count) * 3.0 + SUM(view_count) * 0.1) / COUNT(*) as resonance_score
FROM contents
WHERE status = 'approved'
GROUP BY user_id;
```

## 性能优化建议

### 1. 缓存策略
- 用户信息缓存 (Redis, 30分钟)
- 热门内容缓存 (Redis, 10分钟)
- 统计数据缓存 (Redis, 1小时)

### 2. 分页优化
- 使用游标分页替代偏移分页
- 限制最大页数和每页数量

### 3. 数据库优化
- 读写分离
- 索引优化
- 查询缓存

### 4. API限流
- 用户级别限流
- IP级别限流
- 接口级别限流

这个API设计充分利用了数据库的设计优势，提供了完整的功能接口，支持前端应用的所有需求。
