import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "./ui/card"
import { Input } from "./ui/input"
import { <PERSON><PERSON> } from "./ui/button"
import { Badge } from "./ui/badge"
import { Avatar, AvatarFallback } from "./ui/avatar"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "./ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select"
import { Checkbox } from "./ui/checkbox"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "./ui/dropdown-menu"
import { Search, Filter, MoreHorizontal, UserPlus, Download, Ban, CheckCircle } from "lucide-react"

interface User {
  id: string
  name: string
  email: string
  status: 'active' | 'inactive' | 'banned'
  joinDate: string
  contentCount: number
  resonanceCount: number
  avatar?: string
}

const mockUsers: User[] = [
  { id: '1', name: '张小明', email: '<EMAIL>', status: 'active', joinDate: '2024-01-15', contentCount: 42, resonanceCount: 156 },
  { id: '2', name: '李小红', email: '<EMAIL>', status: 'active', joinDate: '2024-01-18', contentCount: 38, resonanceCount: 142 },
  { id: '3', name: '王小军', email: '<EMAIL>', status: 'inactive', joinDate: '2024-01-20', contentCount: 12, resonanceCount: 34 },
  { id: '4', name: '陈小美', email: '<EMAIL>', status: 'active', joinDate: '2024-01-22', contentCount: 67, resonanceCount: 289 },
  { id: '5', name: '刘小强', email: '<EMAIL>', status: 'banned', joinDate: '2024-01-25', contentCount: 5, resonanceCount: 8 },
  { id: '6', name: '周小丽', email: '<EMAIL>', status: 'active', joinDate: '2024-01-28', contentCount: 23, resonanceCount: 78 },
]

export function UserManagement() {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])

  const filteredUsers = mockUsers.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || user.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const handleSelectUser = (userId: string) => {
    setSelectedUsers(prev => 
      prev.includes(userId) 
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    )
  }

  const handleSelectAll = () => {
    setSelectedUsers(prev => 
      prev.length === filteredUsers.length 
        ? [] 
        : filteredUsers.map(user => user.id)
    )
  }

  const getStatusBadge = (status: User['status']) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-700 hover:bg-green-100">活跃</Badge>
      case 'inactive':
        return <Badge variant="secondary">非活跃</Badge>
      case 'banned':
        return <Badge variant="destructive">已封禁</Badge>
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作栏 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            用户管理
          </h1>
          <p className="text-muted-foreground">管理平台所有用户账户</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="gap-2">
            <Download className="w-4 h-4" />
            导出数据
          </Button>
          <Button className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 gap-2">
            <UserPlus className="w-4 h-4" />
            添加用户
          </Button>
        </div>
      </div>

      {/* 搜索和筛选栏 */}
      <Card className="bg-gradient-to-br from-card to-card/50 backdrop-blur-sm border-0">
        <CardContent className="p-4">
          <div className="flex gap-4 items-center">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索用户名或邮箱..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-32">
                <Filter className="w-4 h-4 mr-2" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                <SelectItem value="active">活跃</SelectItem>
                <SelectItem value="inactive">非活跃</SelectItem>
                <SelectItem value="banned">已封禁</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* 批量操作栏 */}
      {selectedUsers.length > 0 && (
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <span className="text-sm">已选择 {selectedUsers.length} 个用户</span>
              <div className="flex gap-2">
                <Button size="sm" variant="outline" className="gap-2">
                  <CheckCircle className="w-4 h-4" />
                  批量激活
                </Button>
                <Button size="sm" variant="outline" className="gap-2">
                  <Ban className="w-4 h-4" />
                  批量封禁
                </Button>
                <Button size="sm" variant="outline" className="gap-2">
                  <Download className="w-4 h-4" />
                  导出选中
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 用户列表 */}
      <Card className="bg-gradient-to-br from-card to-card/50 backdrop-blur-sm border-0">
        <CardHeader>
          <CardTitle>用户列表</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <Checkbox
                    checked={selectedUsers.length === filteredUsers.length}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
                <TableHead>用户</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>加入时间</TableHead>
                <TableHead>内容数</TableHead>
                <TableHead>共振数</TableHead>
                <TableHead className="w-12"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredUsers.map((user) => (
                <TableRow key={user.id} className="hover:bg-muted/30">
                  <TableCell>
                    <Checkbox
                      checked={selectedUsers.includes(user.id)}
                      onCheckedChange={() => handleSelectUser(user.id)}
                    />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar className="w-8 h-8">
                        <AvatarFallback className="bg-gradient-to-br from-blue-400 to-purple-500 text-white text-xs">
                          {user.name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{user.name}</p>
                        <p className="text-sm text-muted-foreground">{user.email}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge(user.status)}</TableCell>
                  <TableCell>{user.joinDate}</TableCell>
                  <TableCell>{user.contentCount}</TableCell>
                  <TableCell>{user.resonanceCount}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>查看详情</DropdownMenuItem>
                        <DropdownMenuItem>编辑用户</DropdownMenuItem>
                        <DropdownMenuItem>重置密码</DropdownMenuItem>
                        <DropdownMenuItem className="text-destructive">
                          {user.status === 'banned' ? '解除封禁' : '封禁用户'}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}